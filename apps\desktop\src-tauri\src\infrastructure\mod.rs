/// 基础设施层模块
/// 遵循 Tauri 开发规范的分层架构设计
pub mod database;
pub mod connection_pool;

#[cfg(test)]
mod database_integration_test;
pub mod file_system;
pub mod filename_utils;
pub mod performance;
pub mod event_bus;
pub mod ffmpeg;
pub mod ffmpeg_watermark;
pub mod monitoring;
pub mod logging;
pub mod gemini_service;
pub mod video_generation_service;
pub mod image_editing_service;
pub mod tolerant_json_parser;
pub mod markdown_parser;
pub mod bowong_text_video_agent_service;

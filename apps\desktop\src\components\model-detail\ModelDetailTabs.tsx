import React, { useEffect } from 'react';
import { Model, ModelDynamic } from '../../types/model';
import { VideoGenerationTask } from '../../types/videoGeneration';
import { OutfitImageRecord } from '../../types/outfitImage';

export type TabId = 'overview' | 'photos' | 'dynamics' | 'videos' | 'outfits' | 'stats';

interface TabConfig {
  id: TabId;
  name: string;
  icon: string;
  badge: number | null;
  shortcut: string;
}

interface ModelDetailTabsProps {
  activeTab: TabId;
  onTabChange: (tabId: TabId) => void;
  model: Model;
  dynamics: ModelDynamic[];
  videoTasks: VideoGenerationTask[];
  outfitRecords: OutfitImageRecord[];
  onOpenOutfitModal?: () => void;
}

/**
 * 模特详情页面Tab导航组件
 * 包含tab切换和键盘快捷键支持
 */
export const ModelDetailTabs: React.FC<ModelDetailTabsProps> = ({
  activeTab,
  onTabChange,
  model,
  dynamics,
  videoTasks,
  outfitRecords,
  onOpenOutfitModal
}) => {
  // 键盘快捷键支持
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case '1':
            event.preventDefault();
            onTabChange('overview');
            break;
          case '2':
            event.preventDefault();
            onTabChange('photos');
            break;
          case '3':
            event.preventDefault();
            onTabChange('dynamics');
            break;
          case '4':
            event.preventDefault();
            onTabChange('videos');
            break;
          case '5':
            event.preventDefault();
            onTabChange('outfits');
            break;
          case '6':
            event.preventDefault();
            onTabChange('stats');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [onTabChange]);

  const tabs: TabConfig[] = [
    {
      id: 'overview',
      name: '概览',
      icon: '📊',
      badge: null,
      shortcut: 'Ctrl+1'
    },
    {
      id: 'photos',
      name: '照片管理',
      icon: '📸',
      badge: model.photos.length,
      shortcut: 'Ctrl+2'
    },
    {
      id: 'outfits',
      name: '穿搭生成',
      icon: '👗',
      badge: outfitRecords.length,
      shortcut: 'Ctrl+5'
    },
    {
      id: 'videos',
      name: '视频生成',
      icon: '🎬',
      badge: videoTasks.length,
      shortcut: 'Ctrl+4'
    },
    {
      id: 'dynamics',
      name: '模特动态',
      icon: '✨',
      badge: dynamics.length,
      shortcut: 'Ctrl+3'
    },
    {
      id: 'stats',
      name: '数据统计',
      icon: '📈',
      badge: null,
      shortcut: 'Ctrl+6'
    }
  ];

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-200/50 mb-6">
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6" aria-label="Tabs">
          {tabs.map((tab) => {
            // 穿搭生成选项卡特殊处理 - 显示为按钮形式
            if (tab.id === 'outfits') {
              return (
                <button
                  key={tab.id}
                  onClick={() => {
                    onTabChange(tab.id);
                    onOpenOutfitModal?.();
                  }}
                  title={`${tab.name} (${tab.shortcut})`}
                  className={`${activeTab === tab.id
                    ? 'border-purple-500 text-purple-600 bg-purple-50'
                    : 'border-transparent text-gray-500 hover:text-purple-600 hover:border-purple-300 hover:bg-purple-50'
                    } whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm transition-all duration-200 rounded-t-lg flex items-center gap-2 relative group`}
                >
                  <span className="text-base">{tab.icon}</span>
                  <span>{tab.name}</span>
                  {tab.badge !== null && tab.badge > 0 && (
                    <span className={`${activeTab === tab.id
                      ? 'bg-purple-500 text-white'
                      : 'bg-gray-400 text-white group-hover:bg-purple-500'
                      } text-xs px-2 py-0.5 rounded-full font-semibold transition-colors duration-200`}>
                      {tab.badge}
                    </span>
                  )}
                </button>
              );
            }

            // 其他选项卡正常处理
            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                title={`${tab.name} (${tab.shortcut})`}
                className={`${activeTab === tab.id
                  ? 'border-primary-500 text-primary-600 bg-primary-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
                  } whitespace-nowrap py-4 px-4 border-b-2 font-medium text-sm transition-all duration-200 rounded-t-lg flex items-center gap-2 relative group`}
              >
                <span className="text-base">{tab.icon}</span>
                <span>{tab.name}</span>
                {tab.badge !== null && tab.badge > 0 && (
                  <span className={`${activeTab === tab.id
                    ? 'bg-primary-500 text-white'
                    : 'bg-gray-400 text-white group-hover:bg-gray-500'
                    } text-xs px-2 py-0.5 rounded-full font-semibold transition-colors duration-200`}>
                    {tab.badge}
                  </span>
                )}
              </button>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

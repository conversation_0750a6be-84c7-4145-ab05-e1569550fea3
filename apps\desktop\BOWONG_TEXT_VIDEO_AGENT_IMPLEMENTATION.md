# BowongTextVideoAgent 服务实现文档

## 概述

本文档描述了 BowongTextVideoAgent 服务在 Tauri 桌面应用中的完整实现。该实现遵循正确的 Tauri 架构模式：**Rust 后端处理 API 集成，TypeScript 前端通过 Tauri invoke 调用与后端通信**。

## 架构设计

### 正确的 Tauri 架构模式
```
Frontend (TypeScript/React) 
    ↓ (Tauri invoke calls)
Backend (Rust) 
    ↓ (HTTP requests)
External API (BowongTextVideoAgent)
```

### 四层架构实现

#### 1. 数据层 (Data Layer)
- **文件**: `apps/desktop/src-tauri/src/data/models/bowong_text_video_agent.rs`
- **内容**: 完整的数据模型定义 (300+ 行)
- **功能**: 
  - 所有 API 请求/响应结构体
  - 12 个主要模块的数据模型
  - Serde 序列化/反序列化支持

#### 2. 基础设施层 (Infrastructure Layer)
- **文件**: `apps/desktop/src-tauri/src/infrastructure/bowong_text_video_agent_service.rs`
- **内容**: HTTP 客户端服务实现 (600+ 行)
- **功能**:
  - 完整的 HTTP 客户端封装
  - 所有 12 个 API 模块的方法实现
  - 错误处理和重试机制
  - 任务轮询和状态管理

#### 3. 表示层 (Presentation Layer)
- **文件**: `apps/desktop/src-tauri/src/presentation/commands/bowong_text_video_agent_commands.rs`
- **内容**: Tauri 命令接口 (689+ 行)
- **功能**:
  - 45+ 个 Tauri 命令
  - 统一的命令命名规范 (`bowong_*`)
  - 完整的错误处理

#### 4. 前端服务层 (Frontend Service Layer)
- **文件**: `apps/desktop/src/services/bowongTextVideoAgentService.ts`
- **内容**: TypeScript 服务类 (750+ 行)
- **功能**:
  - 完整的前端 API 封装
  - Tauri invoke 调用集成
  - 类型安全的接口定义

## 主要功能模块

### 1. 提示词预处理模块
- 获取示例提示词
- 健康检查
- 提示词验证

### 2. 文件上传模块
- 通用文件上传
- S3 文件上传
- COS 文件上传
- 文件健康检查

### 3. 模板管理模块
- 获取模板列表
- 创建/更新/删除模板
- 任务类型检查
- 提示词检查

### 4. Midjourney 图像生成模块
- 同步图像生成
- 异步图像生成
- 任务状态查询
- 图像描述

### 5. 视频生成模块
- 异步视频生成
- 任务状态查询
- 批量状态查询

### 6. 任务管理模块
- 创建任务
- 查询任务状态
- 任务生命周期管理

### 7. 302AI 集成模块
- Midjourney 集成
- 极梦视频集成
- VEO 视频集成
- 任务取消和状态查询

### 8. 海螺语音模块
- 语音生成
- 语音列表获取
- 音频文件上传
- 语音克隆

### 9. 聚合接口模块
- 图像模型列表
- 视频模型列表
- 统一图像生成
- 统一视频生成

### 10. ComfyUI 集成模块
- 运行节点查询
- 任务提交
- 工作流执行
- 状态查询

### 11. Hedra 集成模块
- 文件上传
- 任务提交
- 状态查询

### 12. FFMPEG 处理模块
- 任务状态查询
- 媒体切片处理

## 技术特性

### Rust 后端特性
- **异步编程**: 使用 Tokio 进行异步 HTTP 请求
- **错误处理**: 使用 anyhow 和 thiserror 进行错误管理
- **序列化**: 使用 Serde 进行 JSON 序列化/反序列化
- **HTTP 客户端**: 使用 reqwest 进行 HTTP 通信
- **配置管理**: 灵活的配置系统
- **任务轮询**: 长时间运行任务的状态轮询机制

### TypeScript 前端特性
- **类型安全**: 完整的 TypeScript 类型定义
- **Tauri 集成**: 正确使用 Tauri invoke API
- **错误处理**: 统一的错误处理机制
- **重试机制**: 网络请求重试支持
- **缓存支持**: 可选的响应缓存

### 状态管理
- **AppState 集成**: 服务实例在应用状态中管理
- **线程安全**: 使用 Mutex 保证线程安全
- **生命周期管理**: 正确的服务初始化和清理

## 测试覆盖

### Rust 单元测试
- 服务创建和配置验证
- 数据模型序列化/反序列化
- 错误处理机制
- 任务轮询逻辑

### TypeScript 单元测试
- 服务初始化测试
- API 调用测试
- 错误处理测试
- 配置管理测试

### 集成测试
- 端到端架构验证
- 数据模型完整性测试
- 服务状态管理测试

## 使用示例

### 初始化服务
```rust
// Rust 后端
let config = BowongTextVideoAgentConfig {
    base_url: "https://api.bowong.com".to_string(),
    api_key: "your-api-key".to_string(),
    timeout: Some(30),
    retry_attempts: Some(3),
    enable_cache: Some(true),
    max_concurrency: Some(10),
};

state.initialize_bowong_service(config)?;
```

```typescript
// TypeScript 前端
const config = {
  baseUrl: 'https://api.bowong.com',
  apiKey: 'your-api-key',
  timeout: 30000,
  retryAttempts: 3,
  enableCache: true,
  maxConcurrency: 10
};

const service = new BowongTextVideoAgentService(config);
```

### 图像生成示例
```typescript
// 同步图像生成
const imageRequest = {
  prompt: 'A beautiful landscape',
  img_file: 'https://example.com/ref.jpg',
  max_wait_time: 120,
  poll_interval: 2
};

const result = await service.syncGenerateImage(imageRequest);
```

### 视频生成示例
```typescript
// 异步视频生成
const videoRequest = {
  prompt: 'A video of nature',
  img_url: 'https://example.com/ref.jpg',
  duration: 10,
  max_wait_time: 300,
  poll_interval: 5
};

const task = await service.asyncGenerateVideo(videoRequest);
const status = await service.queryVideoTaskStatus(task.task_id);
```

## 部署和配置

### 环境要求
- Rust 1.70+
- Node.js 18+
- Tauri 2.0+

### 配置文件
服务配置通过 `BowongTextVideoAgentConfig` 结构体管理，支持：
- API 基础 URL
- API 密钥
- 超时设置
- 重试次数
- 缓存启用
- 最大并发数

### 错误处理
- 网络错误自动重试
- 超时错误处理
- API 错误码映射
- 用户友好的错误消息

## 性能优化

### 并发控制
- 最大并发请求数限制
- 连接池管理
- 请求队列机制

### 缓存策略
- 可选的响应缓存
- 智能缓存失效
- 内存使用优化

### 资源管理
- 自动连接清理
- 内存泄漏防护
- 优雅的服务关闭

## 总结

BowongTextVideoAgent 服务的实现完全遵循了 Tauri 桌面应用的最佳实践：

1. **正确的架构模式**: Rust 后端处理业务逻辑，TypeScript 前端负责用户界面
2. **完整的功能覆盖**: 支持所有 12 个主要 API 模块，45+ 个具体功能
3. **类型安全**: 完整的类型定义和编译时检查
4. **错误处理**: 全面的错误处理和用户反馈机制
5. **测试覆盖**: 单元测试、集成测试和端到端测试
6. **性能优化**: 并发控制、缓存策略和资源管理

该实现为 Tauri 桌面应用提供了一个完整、可靠、高性能的 AI 服务集成解决方案。

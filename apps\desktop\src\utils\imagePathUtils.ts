import { convertFileSrc } from '@tauri-apps/api/core';

/**
 * 处理图片路径，区分本地路径和云端URL
 * @param filePath 文件路径或URL
 * @returns 处理后的图片源地址
 */
export const getImageSrc = (filePath: string): string => {
  // 如果是云端URL（以http://或https://开头），直接返回
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
    return filePath;
  }
  // 如果是本地路径，使用convertFileSrc转换
  return convertFileSrc(filePath);
};

/**
 * 检查路径是否为云端URL
 * @param filePath 文件路径
 * @returns 是否为云端URL
 */
export const isCloudUrl = (filePath: string): boolean => {
  return filePath.startsWith('http://') || filePath.startsWith('https://');
};

/**
 * 检查路径是否为本地文件路径
 * @param filePath 文件路径
 * @returns 是否为本地路径
 */
export const isLocalPath = (filePath: string): boolean => {
  return !isCloudUrl(filePath);
};

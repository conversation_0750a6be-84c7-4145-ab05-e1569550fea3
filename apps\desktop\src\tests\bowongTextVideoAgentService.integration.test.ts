/**
 * BowongTextVideoAgent 服务集成测试
 * 测试与实际 API 的交互（需要真实的服务端点）
 */

import { describe, it, expect, beforeAll, beforeEach } from 'vitest';
import {
  BowongTextVideoAgentFastApiService,
  createBowongTextVideoAgentService,
} from '../services/bowongTextVideoAgentService';
import {
  BowongTextVideoAgentConfig,
  TaskStatus,
} from '../types/bowongTextVideoAgent';

// 集成测试配置
const INTEGRATION_CONFIG: BowongTextVideoAgentConfig = {
  baseUrl: process.env.BOWONG_API_BASE_URL || 'http://localhost:8000',
  apiKey: process.env.BOWONG_API_KEY || 'test-api-key',
  timeout: 30000,
  retryAttempts: 2,
  retryDelay: 1000,
};

// 跳过集成测试的条件
const SKIP_INTEGRATION = process.env.SKIP_INTEGRATION_TESTS === 'true';

describe.skipIf(SKIP_INTEGRATION)('BowongTextVideoAgent 集成测试', () => {
  let service: BowongTextVideoAgentFastApiService;

  beforeAll(() => {
    service = createBowongTextVideoAgentService(INTEGRATION_CONFIG);
  });

  beforeEach(async () => {
    // 等待一段时间避免 API 限流
    await new Promise(resolve => setTimeout(resolve, 1000));
  });

  describe('服务连接测试', () => {
    it('应该能够连接到服务', async () => {
      const isConnected = await service.testConnection();
      expect(isConnected).toBe(true);
    }, 10000);

    it('健康检查应该返回正常状态', async () => {
      const promptHealth = await service.checkPromptHealth();
      expect(promptHealth.status).toBe(true);

      const fileHealth = await service.checkFileHealth();
      expect(fileHealth.status).toBe(true);
    }, 10000);
  });

  describe('提示词预处理模块', () => {
    it('应该能够获取示例提示词', async () => {
      const result = await service.getSamplePrompt({ task_type: 'video' });
      expect(result).toBeDefined();
    }, 10000);

    it('应该能够检查提示词', async () => {
      const result = await service.checkPrompt({ prompt: 'a beautiful landscape' });
      expect(result.status).toBe(true);
    }, 10000);
  });

  describe('文件操作模块', () => {
    it('应该能够上传文件', async () => {
      // 创建测试文件
      const testContent = 'This is a test file for integration testing';
      const testFile = new File([testContent], 'test.txt', { type: 'text/plain' });

      const result = await service.uploadFile({ file: testFile });
      expect(result.status).toBe(true);
      expect(result.data).toBeDefined();
    }, 15000);

    it('应该能够上传文件到 S3', async () => {
      const testContent = 'This is a test file for S3 upload';
      const testFile = new File([testContent], 'test-s3.txt', { type: 'text/plain' });

      const result = await service.uploadFileToS3({ file: testFile });
      expect(result.status).toBe(true);
      expect(result.data).toBeDefined();
    }, 15000);
  });

  describe('视频模板管理模块', () => {
    it('应该能够获取模板列表', async () => {
      const result = await service.getTemplates({ page: 1, page_size: 5 });
      expect(result.status).toBe(true);
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.page).toBe(1);
      expect(result.page_size).toBe(5);
    }, 10000);

    it('应该能够检查任务类型', async () => {
      const result = await service.checkTaskType({ task_type: 'video' });
      expect(result.status).toBe(true);
    }, 10000);

    it('应该能够创建和删除模板', async () => {
      const templateData = {
        prompt: 'Integration test template',
        cover_url: 'https://example.com/cover.jpg',
        video_url: 'https://example.com/video.mp4',
        description: 'Test template for integration testing',
        detailDescription: 'Detailed description for integration test template',
        title_zh: '集成测试模板',
        aspect_ratio: '16:9',
        engine: 'test-engine',
        presetPrompts: 'test preset prompts',
        task_type: 'video',
      };

      // 创建模板
      const createResult = await service.createTemplate(templateData);
      expect(createResult.status).toBe(true);
      expect(createResult.data?.id).toBeDefined();

      const templateId = createResult.data!.id!;

      // 删除模板
      const deleteResult = await service.deleteTemplate(templateId);
      expect(deleteResult.status).toBe(true);
    }, 20000);
  });

  describe('任务管理模块', () => {
    it('应该能够创建任务', async () => {
      const taskRequest = {
        task_type: 'test',
        prompt: 'Integration test task',
        ar: '16:9',
      };

      const result = await service.createTask(taskRequest);
      expect(result.task_id).toBeDefined();
      expect(Object.values(TaskStatus)).toContain(result.status);
    }, 15000);

    it('应该能够查询任务状态', async () => {
      // 首先创建一个任务
      const taskRequest = {
        task_type: 'test',
        prompt: 'Status query test task',
      };

      const createResult = await service.createTask(taskRequest);
      const taskId = createResult.task_id;

      // 查询任务状态
      const statusResult = await service.getTaskStatus(taskId);
      expect(statusResult.task_id).toBe(taskId);
      expect(Object.values(TaskStatus)).toContain(statusResult.status);
    }, 20000);
  });

  describe('Midjourney 图片生成模块', () => {
    it('应该能够异步生成图片', async () => {
      const request = {
        prompt: 'a beautiful sunset over mountains, digital art',
      };

      const result = await service.asyncGenerateImage(request);
      expect(result.task_id).toBeDefined();
      expect(Object.values(TaskStatus)).toContain(result.status);
    }, 15000);

    it('应该能够查询图片生成任务状态', async () => {
      const request = {
        prompt: 'a serene lake with reflection, oil painting style',
      };

      const createResult = await service.asyncGenerateImage(request);
      const taskId = createResult.task_id;

      const statusResult = await service.queryImageTaskStatus(taskId);
      expect(statusResult.task_id).toBe(taskId);
      expect(Object.values(TaskStatus)).toContain(statusResult.status);
    }, 20000);
  });

  describe('极梦视频生成模块', () => {
    it('应该能够异步生成视频', async () => {
      const request = {
        prompt: 'a flowing river through a forest, cinematic style',
        duration: 5,
        model_type: 'lite' as const,
      };

      const result = await service.asyncGenerateVideo(request);
      expect(result.task_id).toBeDefined();
      expect(Object.values(TaskStatus)).toContain(result.status);
    }, 15000);

    it('应该能够查询视频生成任务状态', async () => {
      const request = {
        prompt: 'clouds moving across the sky, time-lapse style',
        duration: 3,
      };

      const createResult = await service.asyncGenerateVideo(request);
      const taskId = createResult.task_id;

      const statusResult = await service.queryVideoTaskStatus(taskId);
      expect(statusResult.task_id).toBe(taskId);
      expect(Object.values(TaskStatus)).toContain(statusResult.status);
    }, 20000);

    it('应该能够批量查询视频任务状态', async () => {
      // 创建多个视频生成任务
      const requests = [
        { prompt: 'ocean waves, peaceful scene', duration: 3 },
        { prompt: 'mountain landscape, aerial view', duration: 3 },
      ];

      const taskIds: string[] = [];
      for (const request of requests) {
        const result = await service.asyncGenerateVideo(request);
        taskIds.push(result.task_id);
      }

      // 批量查询状态
      const batchResult = await service.batchQueryVideoStatus({ job_ids: taskIds });
      expect(batchResult.status).toBe(true);
      expect(batchResult.data).toBeDefined();
    }, 30000);
  });

  describe('聚合接口模块', () => {
    it('应该能够获取图片模型列表', async () => {
      const result = await service.getImageModelList();
      expect(Array.isArray(result.models)).toBe(true);
      expect(result.models.length).toBeGreaterThan(0);
    }, 10000);

    it('应该能够获取视频模型列表', async () => {
      const result = await service.getVideoModelList();
      expect(Array.isArray(result.models)).toBe(true);
      expect(result.models.length).toBeGreaterThan(0);
    }, 10000);
  });

  describe('海螺API模块', () => {
    it('应该能够获取音色列表', async () => {
      const result = await service.getVoices();
      expect(Array.isArray(result.voices)).toBe(true);
    }, 10000);

    it('应该能够生成语音', async () => {
      const request = {
        text: '这是一个集成测试的语音合成示例',
        voice_id: 'default',
        speed: 1.0,
        vol: 1.0,
      };

      const result = await service.generateSpeech(request);
      expect(result.status).toBe(true);
    }, 15000);
  });

  describe('错误处理和重试机制', () => {
    it('应该正确处理无效的提示词', async () => {
      try {
        await service.checkPrompt({ prompt: '' });
      } catch (error: any) {
        expect(error.name).toMatch(/ValidationError|BowongTextVideoAgentError/);
      }
    }, 10000);

    it('应该正确处理不存在的任务ID', async () => {
      try {
        await service.getTaskStatus('non-existent-task-id');
      } catch (error: any) {
        expect(error.name).toMatch(/TaskFailedError|BowongTextVideoAgentError/);
      }
    }, 10000);
  });

  describe('性能测试', () => {
    it('并发请求应该正常处理', async () => {
      const promises = Array.from({ length: 5 }, (_, i) =>
        service.getSamplePrompt({ task_type: `test-${i}` })
      );

      const results = await Promise.allSettled(promises);
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      
      // 至少有一半的请求应该成功
      expect(successCount).toBeGreaterThanOrEqual(Math.floor(promises.length / 2));
    }, 30000);

    it('大文件上传应该正常处理', async () => {
      // 创建一个较大的测试文件 (1MB)
      const largeContent = 'x'.repeat(1024 * 1024);
      const largeFile = new File([largeContent], 'large-test.txt', { type: 'text/plain' });

      const result = await service.uploadFile({ file: largeFile });
      expect(result.status).toBe(true);
      expect(result.data).toBeDefined();
    }, 60000);
  });
});

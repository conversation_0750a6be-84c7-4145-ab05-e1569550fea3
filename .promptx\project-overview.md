# MixVideo 项目概览与开发规范

## 项目简介

MixVideo 是一个基于 Tauri 2.0 构建的跨平台AI视频创作桌面应用，采用 Rust + React + TypeScript 技术栈，旨在构建全球领先的AI驱动视频创作生态平台。

### 核心愿景
**"构建全球领先的AI驱动视频创作生态平台，让每个人都能轻松创作出专业级的视频内容"**

## 技术架构

### 整体架构
```
mixvideo/
├── apps/
│   └── desktop/           # Tauri桌面应用
│       ├── src/           # React前端代码
│       │   ├── components/    # UI组件
│       │   ├── pages/         # 页面组件
│       │   ├── store/         # 状态管理(Zustand)
│       │   ├── services/      # 业务服务
│       │   ├── types/         # TypeScript类型定义
│       │   ├── hooks/         # React Hooks
│       │   ├── utils/         # 工具函数
│       │   └── styles/        # 样式文件
│       └── src-tauri/     # Rust后端代码
│           ├── src/
│           │   ├── infrastructure/   # 基础设施层
│           │   ├── data/            # 数据访问层
│           │   ├── business/        # 业务逻辑层
│           │   ├── presentation/    # 表现层(Commands)
│           │   └── services/        # 服务层
│           └── Cargo.toml
├── packages/              # 共享包(未来扩展)
├── docs/                  # 项目文档
└── .promptx/             # 开发规范文档
```

### 前端技术栈
- **框架**: React 18 + TypeScript 5.8
- **构建工具**: Vite 6.0
- **状态管理**: Zustand 4.4
- **UI框架**: TailwindCSS 3.4
- **图标**: Lucide React + Heroicons
- **路由**: React Router DOM 6.20
- **测试**: Vitest + Testing Library
- **包管理**: PNPM 8.15 (Workspace)

### 后端技术栈
- **框架**: Tauri 2.0 + Rust 1.70+
- **数据库**: SQLite (WAL模式，支持连接池)
- **AI集成**: Google Gemini API
- **多媒体**: FFmpeg
- **异步**: Tokio + async/await
- **序列化**: Serde + JSON
- **错误处理**: anyhow + thiserror

## 核心功能模块

### 1. 项目管理
- 多项目管理，本地路径绑定
- 素材管理：视频、音频、图片统一管理
- 版本控制：素材版本追踪，支持回滚

### 2. AI智能分析
- 基于Gemini AI的视频内容自动分类
- 场景检测：自动识别视频场景变化，智能分割片段
- 质量评估：AI评估视频质量，提供优化建议

### 3. 模板系统
- 可视化模板编辑器
- AI驱动的素材与模板自动匹配
- 批量处理：一键应用模板到多个素材

### 4. 模特管理
- 完整的模特信息管理系统
- 支持多类型照片分类存储
- 素材与模特自动关联

### 5. 穿搭推荐
- AI驱动的穿搭搭配推荐
- 智能搜索和筛选
- 收藏和比较功能

### 6. 视频生成
- 基于模板的视频自动生成
- 支持多种输出格式
- 批量导出处理

## 四层架构设计

### 1. 基础设施层 (Infrastructure)
- **数据库管理**: SQLite连接池，读写分离
- **外部服务**: Gemini API, FFmpeg集成
- **文件系统**: 文件操作，路径管理
- **日志系统**: 结构化日志，性能监控
- **事件总线**: 组件间通信

### 2. 数据访问层 (Data)
- **模型定义**: 所有业务实体模型
- **仓库模式**: 数据访问抽象
- **数据库迁移**: 版本管理
- **连接管理**: 连接池优化

### 3. 业务逻辑层 (Business)
- **服务层**: 核心业务逻辑
- **领域模型**: 业务规则封装
- **工作流**: 复杂业务流程
- **验证**: 数据验证规则

### 4. 表现层 (Presentation)
- **Tauri Commands**: 前后端通信接口
- **参数验证**: 输入参数校验
- **错误处理**: 统一错误响应
- **权限控制**: 操作权限管理

## 开发环境配置

### 环境要求
- Node.js >= 18.0.0
- Rust >= 1.70.0
- PNPM >= 8.0.0
- Tauri CLI >= 2.0.0

### 开发命令
```bash
# 安装依赖
pnpm install

# 开发模式
pnpm tauri:dev

# 构建应用
pnpm tauri:build

# 运行测试
pnpm test

# 代码格式化
pnpm format

# 代码检查
pnpm lint
```

## 核心设计原则

### 1. 安全第一
- 数据库文件安全存储
- 输入参数严格验证
- 错误信息不泄露敏感数据

### 2. 性能优化
- 数据库连接池
- 读写分离
- 虚拟化列表
- 懒加载

### 3. 用户体验
- 响应式设计
- 优雅的加载状态
- 友好的错误提示
- 流畅的动画效果

### 4. 可维护性
- 清晰的分层架构
- 统一的编码规范
- 完善的测试覆盖
- 详细的文档说明

### 5. 可扩展性
- 模块化设计
- 插件化架构
- 配置化管理
- 微服务准备

## 数据库设计

### 核心表结构
- **projects**: 项目管理
- **materials**: 素材管理
- **models**: 模特信息
- **templates**: 模板定义
- **video_classifications**: AI分类记录
- **outfit_images**: 穿搭图片
- **conversations**: 对话记录

### 数据库特性
- WAL模式：提高并发性能
- 外键约束：保证数据一致性
- 索引优化：提升查询性能
- 连接池：支持高并发访问

## 状态管理规范

### Zustand Store 设计
- **uiStore**: UI状态管理
- **projectStore**: 项目状态
- **materialStore**: 素材状态
- **outfitSearchStore**: 穿搭搜索状态
- **videoClassificationStore**: 视频分类状态

### 状态管理原则
- 单一数据源
- 不可变更新
- 异步操作处理
- 错误状态管理

## UI/UX 设计规范

### 设计系统
- **颜色系统**: 现代蓝色主题，语义化颜色
- **字体系统**: Inter + JetBrains Mono
- **间距系统**: 8px基础网格
- **圆角系统**: 统一的圆角规范
- **阴影系统**: 层次化阴影效果

### 组件规范
- 原子化设计
- 可复用组件
- 一致的交互模式
- 无障碍访问支持

### 动画规范
- 流畅的过渡效果
- 有意义的动画反馈
- 性能优化的动画
- 可配置的动画偏好

## 测试策略

### 测试类型
- **单元测试**: 核心业务逻辑
- **集成测试**: API和数据库集成
- **组件测试**: React组件测试
- **E2E测试**: 用户界面端到端测试

### 测试工具
- **前端**: Vitest + Testing Library
- **后端**: Rust内置测试框架
- **E2E**: 计划使用Playwright

### 测试覆盖率
- 目标覆盖率: 80%+
- 关键路径: 100%覆盖
- 持续集成: 自动化测试

## 版本管理

### 版本号规范
- 语义化版本: MAJOR.MINOR.PATCH
- 当前版本: v0.2.1
- 发布周期: 迭代式发布

### Git工作流
- 主分支: master
- 功能分支: feature/功能名
- 修复分支: hotfix/问题描述
- 发布分支: release/版本号

## 部署与发布

### 构建产物
- Windows: .msi + .exe
- macOS: .dmg + .app
- Linux: .deb + .rpm + .AppImage

### 自动化发布
- GitHub Actions
- 自动构建
- 自动发布
- 版本标签管理

## 性能监控

### 监控指标
- 应用启动时间
- 数据库查询性能
- AI分类处理时间
- 内存使用情况

### 优化策略
- 数据库查询优化
- 图片懒加载
- 虚拟化列表
- 缓存策略

## 错误处理

### 错误分类
- 用户输入错误
- 系统运行错误
- 网络连接错误
- 外部服务错误

### 处理策略
- 统一错误格式
- 友好错误提示
- 错误日志记录
- 自动重试机制

## 国际化支持

### 多语言支持
- 中文(简体): 主要语言
- 英文: 计划支持
- 其他语言: 未来扩展

### 本地化策略
- 文本外部化
- 日期时间格式
- 数字格式
- 文化适配

## 安全考虑

### 数据安全
- 本地数据加密
- 敏感信息保护
- 安全的文件操作
- API密钥管理

### 应用安全
- 输入验证
- SQL注入防护
- 文件路径验证
- 权限控制

## 未来规划

### 短期目标 (3个月)
- 完善AI分类功能
- 优化用户界面
- 提升性能表现
- 增加测试覆盖

### 中期目标 (6个月)
- 添加更多AI功能
- 支持更多文件格式
- 云端同步功能
- 插件系统

### 长期目标 (1年)
- 多平台支持
- 微服务架构
- 商业化功能
- 生态系统建设

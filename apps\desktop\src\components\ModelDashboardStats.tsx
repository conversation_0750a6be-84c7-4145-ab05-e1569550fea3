import React from 'react';
import {
  PhotoIcon,
  SparklesIcon,
  HeartIcon,
  ClockIcon,
  ArrowTrendingUpIcon,
  CheckCircleIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { ModelDashboardStats } from '../types/outfitImage';

interface ModelDashboardStatsProps {
  stats: ModelDashboardStats;
  loading?: boolean;
}

/**
 * 模特个人看板统计信息组件
 * 遵循 Tauri 开发规范和 UI/UX 设计标准
 */
export const ModelDashboardStatsComponent: React.FC<ModelDashboardStatsProps> = ({
  stats,
  loading = false
}) => {
  // 格式化时间显示
  const formatDuration = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}min`;
  };

  // 格式化百分比
  const formatPercentage = (rate: number): string => {
    return `${(rate * 100).toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 animate-pulse">
        <div className="flex items-center mb-6">
          <div className="w-2 h-8 bg-gray-200 rounded-full mr-4"></div>
          <div className="h-6 bg-gray-200 rounded w-32"></div>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[...Array(8)].map((_, index) => (
            <div key={index} className="p-4 bg-gray-50 rounded-xl">
              <div className="h-8 w-8 bg-gray-200 rounded-lg mb-3"></div>
              <div className="h-4 bg-gray-200 rounded w-16 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-12"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const statCards = [
    {
      title: '总照片',
      value: stats.total_photos,
      icon: PhotoIcon,
      color: 'blue',
      description: `形象照 ${stats.portrait_photos} · 工作照 ${stats.work_photos}`
    },
    {
      title: '穿搭图片',
      value: stats.outfit_stats.total_images,
      icon: SparklesIcon,
      color: 'purple',
      description: `收藏 ${stats.outfit_stats.favorite_images} 张`
    },
    {
      title: '生成记录',
      value: stats.outfit_stats.total_records,
      icon: PlayIcon,
      color: 'green',
      description: `成功 ${stats.outfit_stats.completed_records} · 失败 ${stats.outfit_stats.failed_records}`
    },
    {
      title: '成功率',
      value: formatPercentage(stats.success_rate),
      icon: CheckCircleIcon,
      color: 'emerald',
      description: '生成成功率'
    },
    {
      title: '收藏数',
      value: stats.favorite_count,
      icon: HeartIcon,
      color: 'pink',
      description: '收藏的穿搭图片'
    },
    {
      title: '平均耗时',
      value: formatDuration(stats.average_generation_time_ms),
      icon: ClockIcon,
      color: 'orange',
      description: '平均生成时间'
    },
    {
      title: '最近生成',
      value: stats.recent_generations,
      icon: ArrowTrendingUpIcon,
      color: 'indigo',
      description: '最近30天'
    },
    {
      title: '处理中',
      value: stats.outfit_stats.processing_records,
      icon: PlayIcon,
      color: 'yellow',
      description: '正在处理的任务'
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap: Record<string, { bg: string; text: string; icon: string }> = {
      blue: { bg: 'bg-blue-50', text: 'text-blue-900', icon: 'text-blue-600' },
      purple: { bg: 'bg-purple-50', text: 'text-purple-900', icon: 'text-purple-600' },
      green: { bg: 'bg-green-50', text: 'text-green-900', icon: 'text-green-600' },
      emerald: { bg: 'bg-emerald-50', text: 'text-emerald-900', icon: 'text-emerald-600' },
      pink: { bg: 'bg-pink-50', text: 'text-pink-900', icon: 'text-pink-600' },
      orange: { bg: 'bg-orange-50', text: 'text-orange-900', icon: 'text-orange-600' },
      indigo: { bg: 'bg-indigo-50', text: 'text-indigo-900', icon: 'text-indigo-600' },
      yellow: { bg: 'bg-yellow-50', text: 'text-yellow-900', icon: 'text-yellow-600' }
    };
    return colorMap[color] || colorMap.blue;
  };

  return (
    <div className="bg-gradient-to-br from-white to-primary-50/30 rounded-2xl shadow-sm border border-gray-200/50 p-6 hover:shadow-md transition-all duration-300 hover:-translate-y-1 relative overflow-hidden animate-fade-in">
      <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-purple-100/50 to-purple-200/50 rounded-full -translate-y-8 translate-x-8 opacity-50"></div>

      <div className="relative z-10">
        <div className="flex items-center mb-4">
          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center text-white mr-3">
            <ArrowTrendingUpIcon className="w-5 h-5" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900">个人看板</h3>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
          {statCards.map((card, index) => {
            const Icon = card.icon;
            const colors = getColorClasses(card.color);

            return (
              <div
                key={index}
                className={`p-4 ${colors.bg} rounded-xl hover:scale-105 transition-all duration-200 cursor-pointer group`}
              >
                <div className="flex items-center justify-between mb-3">
                  <Icon className={`w-8 h-8 ${colors.icon} group-hover:scale-110 transition-transform duration-200`} />
                </div>

                <div className={`text-2xl font-bold ${colors.text} mb-1`}>
                  {card.value}
                </div>

                <div className="text-sm font-medium text-gray-600 mb-1">
                  {card.title}
                </div>

                <div className="text-xs text-gray-500">
                  {card.description}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* 状态指示器 */}
      <div className="mt-6 pt-6 border-t border-gray-100">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
              <span className="text-gray-600">已完成 {stats.outfit_stats.completed_records}</span>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
              <span className="text-gray-600">处理中 {stats.outfit_stats.processing_records}</span>
            </div>
            <div className="flex items-center">
              <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
              <span className="text-gray-600">等待中 {stats.outfit_stats.pending_records}</span>
            </div>
            {stats.outfit_stats.failed_records > 0 && (
              <div className="flex items-center">
                <div className="w-2 h-2 bg-red-500 rounded-full mr-2"></div>
                <span className="text-gray-600">失败 {stats.outfit_stats.failed_records}</span>
              </div>
            )}
          </div>
          
          <div className="text-gray-500">
            总耗时 {formatDuration(stats.total_generation_time_ms)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModelDashboardStatsComponent;

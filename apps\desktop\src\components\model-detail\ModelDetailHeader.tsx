import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeftIcon,
  PlusIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { Model } from '../../types/model';
import { LoadingSpinner } from '../LoadingSpinner';

interface ModelDetailHeaderProps {
  model: Model;
  uploadingPhotos: boolean;
  onOpenUploadModal: () => void;
}

/**
 * 模特详情页面头部组件
 * 包含返回按钮、模特信息展示和操作按钮
 */
export const ModelDetailHeader: React.FC<ModelDetailHeaderProps> = ({
  model,
  uploadingPhotos,
  onOpenUploadModal
}) => {
  const navigate = useNavigate();

  return (
    <div className="bg-gradient-to-br from-white to-primary-50/30 rounded-2xl shadow-sm border border-gray-200/50 p-6 mb-6 relative overflow-hidden animate-fade-in">
      {/* 装饰性背景元素 */}
      <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-primary-100/50 to-primary-200/50 rounded-full -translate-y-16 translate-x-16 opacity-50"></div>

      <div className="relative z-10">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-4 mb-4">
              <button
                onClick={() => navigate('/models')}
                className="group flex items-center text-gray-600 hover:text-primary-600 transition-all duration-200 hover:bg-primary-50 px-3 py-2 rounded-lg"
              >
                <ArrowLeftIcon className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-200" />
                <span className="font-medium">返回模特列表</span>
              </button>
            </div>

            <div className="flex items-center gap-4 mb-3">
              <div className="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center text-white font-bold text-2xl shadow-lg">
                {model.name.charAt(0)}
              </div>
              <div>
                <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-1">
                  {model.name}
                </h1>
                {model.stage_name && (
                  <p className="text-gray-600 text-sm lg:text-base">
                    艺名：{model.stage_name}
                  </p>
                )}
              </div>
            </div>

            <div className="flex items-center gap-4 text-sm text-gray-500">
              <div className="flex items-center gap-1.5">
                <UserIcon className="w-4 h-4" />
                <span>{model.gender === 'Male' ? '男' : model.gender === 'Female' ? '女' : '其他'}</span>
              </div>
              {model.age && (
                <div className="flex items-center gap-1.5">
                  <span>{model.age}岁</span>
                </div>
              )}
              <div className="flex items-center gap-1.5">
                <span>{model.photos.length} 张照片</span>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={onOpenUploadModal}
              disabled={uploadingPhotos}
              className="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl hover:from-primary-600 hover:to-primary-700 shadow-sm hover:shadow-md transition-all duration-200 font-medium disabled:opacity-50"
            >
              {uploadingPhotos ? (
                <LoadingSpinner size="small" />
              ) : (
                <PlusIcon className="w-4 h-4" />
              )}
              上传照片
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

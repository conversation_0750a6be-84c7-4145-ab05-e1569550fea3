import React, { useState, useEffect } from 'react';
import { Model, ModelDynamic, ModelDynamicStats } from '../../types/model';
import { modelDynamicService } from '../../services/modelDynamicService';
import ModelDynamicList from '../ModelDynamicList';
import CreateDynamicModal from '../CreateDynamicModal';
import {
  SparklesIcon,
  CheckCircleIcon,
  ClockIcon,
  VideoCameraIcon,
  PlusIcon
} from '@heroicons/react/24/outline';

interface ModelDynamicsTabProps {
  model: Model;
}

/**
 * 模特详情动态Tab组件
 * 包含动态统计、动态列表和创建动态功能
 */
export const ModelDynamicsTab: React.FC<ModelDynamicsTabProps> = ({ model }) => {
  const [dynamics, setDynamics] = useState<ModelDynamic[]>([]);
  const [stats, setStats] = useState<ModelDynamicStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    loadDynamics();
    loadStats();
  }, [model.id]);

  const loadDynamics = async () => {
    try {
      // 使用模拟数据进行开发测试
      const dynamicsList = modelDynamicService.getMockDynamics(model.id);
      setDynamics(dynamicsList);

      // TODO: 后续替换为真实API调用
      // const dynamicsList = await modelDynamicService.getDynamicsByModelId(model.id);
      // setDynamics(dynamicsList);
    } catch (err) {
      console.error('加载动态列表失败:', err);
      setError('加载动态列表失败');
    }
  };

  const loadStats = async () => {
    try {
      // 使用模拟数据进行开发测试
      const statsData = modelDynamicService.getMockStats(model.id);
      setStats(statsData);

      // TODO: 后续替换为真实API调用
      // const statsData = await modelDynamicService.getStatsByModelId(model.id);
      // setStats(statsData);
    } catch (err) {
      console.error('加载统计数据失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateDynamic = async (dynamicData: any) => {
    try {
      // TODO: 实现创建动态的逻辑
      console.log('创建动态:', dynamicData);
      
      // 关闭模态框并刷新数据
      setShowCreateModal(false);
      await loadDynamics();
      await loadStats();
    } catch (err) {
      console.error('创建动态失败:', err);
    }
  };

  return (
    <div className="animate-fade-in space-y-6">
      {/* 动态统计卡片 */}
      <div className="bg-gradient-to-br from-white to-primary-50/30 rounded-2xl shadow-sm border border-gray-200/50 p-6 relative overflow-hidden">
        <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-primary-100/50 to-primary-200/50 rounded-full -translate-y-8 translate-x-8 opacity-50"></div>
        
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center text-white">
                <SparklesIcon className="w-5 h-5" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">模特动态</h3>
                <p className="text-sm text-gray-600">AI生成的视频动态内容</p>
              </div>
            </div>
            
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center gap-2 px-4 py-2.5 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 shadow-sm hover:shadow-md font-medium"
            >
              <PlusIcon className="w-4 h-4" />
              发布动态
            </button>
          </div>

          {/* 统计信息网格 */}
          {stats && (
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/90 transition-all duration-200">
                <div className="flex items-center gap-2 mb-1">
                  <SparklesIcon className="h-4 w-4 text-primary-600" />
                  <span className="text-xs font-medium text-gray-600">总动态</span>
                </div>
                <div className="text-xl font-bold text-gray-900">{stats.total_dynamics}</div>
              </div>

              <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/90 transition-all duration-200">
                <div className="flex items-center gap-2 mb-1">
                  <CheckCircleIcon className="h-4 w-4 text-green-600" />
                  <span className="text-xs font-medium text-gray-600">已发布</span>
                </div>
                <div className="text-xl font-bold text-gray-900">{stats.published_dynamics}</div>
              </div>

              <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/90 transition-all duration-200">
                <div className="flex items-center gap-2 mb-1">
                  <ClockIcon className="h-4 w-4 text-yellow-600" />
                  <span className="text-xs font-medium text-gray-600">生成中</span>
                </div>
                <div className="text-xl font-bold text-gray-900">{stats.generating_videos}</div>
              </div>

              <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-gray-200/50 hover:bg-white/90 transition-all duration-200">
                <div className="flex items-center gap-2 mb-1">
                  <VideoCameraIcon className="h-4 w-4 text-blue-600" />
                  <span className="text-xs font-medium text-gray-600">总视频</span>
                </div>
                <div className="text-xl font-bold text-gray-900">{stats.total_videos}</div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 动态列表 */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-200/50">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center text-white">
                <span className="text-sm">📋</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">动态列表</h3>
            </div>
            <span className="text-sm text-gray-500">{dynamics.length} 条动态</span>
          </div>
        </div>
        
        <div className="p-6">
          <ModelDynamicList
            dynamics={dynamics}
            onRefresh={loadDynamics}
            loading={loading}
            error={error}
          />
        </div>
      </div>

      {/* 创建动态模态框 */}
      {showCreateModal && (
        <CreateDynamicModal
          model={model}
          onSubmit={handleCreateDynamic}
          onCancel={() => setShowCreateModal(false)}
        />
      )}
    </div>
  );
};

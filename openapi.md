# Text Video Agent API 文档

## 概述

Text Video Agent API 是一个综合性的文本生成视频API服务，版本 1.0.6。该API提供了多种AI生成服务，包括图片生成、视频生成、音频生成、口型合成等功能，支持多个AI服务提供商。

## API 分类概览

### 1. 提示词预处理 (Prompt Processing)
- 获取示例提示词
- 健康检测

### 2. 文件操作 (File Operations)
- 文件上传到云存储（COS/S3）
- 健康检测

### 3. 视频模板管理 (Video Template Management)
- 模板CRUD操作
- 任务类型检查
- 分页查询

### 4. Midjourney图片生成 (Midjourney Image Generation)
- 图片生成（同步/异步）
- 图片描述反推
- 提示词预审

### 5. 极梦视频生成 (JiMeng Video Generation)
- 视频生成（同步/异步）
- 任务状态查询

### 6. 任务管理 (Task Management)
- 异步任务提交
- 任务状态查询
- 模板化任务处理

### 7. Midjourney视频生成 (Midjourney Video Generation)
- 视频生成服务
- 任务状态查询

### 8. 302AI服务集成 (302AI Integration)
- Midjourney图片生成
- 极梦视频生成
- Midjourney视频生成
- VEO视频生成
- Hedra口型合成

### 9. 聚合接口 (Union APIs)
- 统一的图片/视频生成接口
- 支持多模型选择

### 10. ComfyUI工作流 (ComfyUI Workflow)
- 工作流执行
- 节点管理

### 11. 海螺API (HaiLuo API)
- 语音合成
- 声音克隆

### 12. Hedra口型合成 (Hedra Lip Sync)
- 口型合成任务
- 文件上传管理

---

## 详细接口分析

### 1. 提示词预处理模块

#### 1.1 获取示例提示词
**接口**: `GET /api/prompt/default`

**参数**:
- `task_type` (可选): 任务类型，用于获取特定类型的示例提示词

**作用**: 为用户提供不同任务类型的示例提示词，帮助用户更好地构建生成请求

**返回**: 示例提示词数据

#### 1.2 健康检测
**接口**: `GET /api/prompt/health`

**作用**: 检查提示词预处理服务的健康状态

---

### 2. 文件操作模块

#### 2.1 上传文件到COS (已弃用)
**接口**: `POST /api/file/upload`

**参数**:
- `file`: 要上传的文件

**作用**: 上传文件到腾讯云COS存储
**状态**: 已弃用，建议使用S3上传接口

#### 2.2 上传文件到S3 (推荐)
**接口**: `POST /api/file/upload/s3`

**参数**:
- `file`: 要上传的文件

**作用**: 上传文件到S3存储，支持CDN加速，性能更好

**返回**: FileUploadResponse
- `status`: 上传状态
- `msg`: 响应消息  
- `data`: 文件URL

---

### 3. 视频模板管理模块

#### 3.1 获取视频模板列表
**接口**: `GET /api/template/default`

**参数**:
- `task_type` (可选): 任务类型筛选
- `page` (可选): 页码，从1开始，默认1
- `page_size` (可选): 每页记录数，默认100，最大1000
- `category` (可选): 模版分类标签，默认"全部"

**作用**: 获取视频模板列表，支持分页和按任务类型筛选

**返回**:
```json
{
  "status": true,
  "data": [模板数据列表],
  "page": 当前页码,
  "page_size": 每页数量,
  "total": 总记录数,
  "total_pages": 总页数
}
```

#### 3.2 检查任务类型可用性
**接口**: `GET /api/template/check/task_type`

**参数**:
- `task_type`: 要检查的任务类型

**作用**: 检查指定的任务类型是否可用（未被占用）

#### 3.3 创建视频模板
**接口**: `POST /api/template/create`

**参数**: 模板数据对象，必须包含task_type字段且唯一

**示例数据**:
```json
{
  "prompt": "Cozy, earthy-style visual...",
  "cover_url": "https://...",
  "video_url": "https://...",
  "description": "茶艺展示",
  "detailDescription": "茶艺仪式与草本排毒茶的自然精华展示",
  "title_zh": "养生草本茶艺展示",
  "aspect_ratio": "9:16",
  "engine": "mj",
  "presetPrompts": "11111\n22222",
  "task_type": "tea"
}
```

#### 3.4 更新视频模板
**接口**: `PUT /api/template/update`

**参数**: 模板数据对象，必须包含id字段

#### 3.5 删除视频模板
**接口**: `DELETE /api/template/delete/{template_id}`

**参数**:
- `template_id`: 要删除的模板ID

**作用**: 软删除视频模板

---

### 4. Midjourney图片生成模块

#### 4.1 图片提示词预审
**接口**: `GET /api/mj/prompt/check`

**参数**:
- `prompt`: 图片生成提示词

**作用**: 预审提示词是否符合Midjourney的生成要求

#### 4.2 同步生成图片
**接口**: `POST /api/mj/sync/image`

**参数**:
- `prompt`: 图片生成提示词
- `img_file` (可选): 样貌参考图片文件
- `max_wait_time` (可选): 最大等待时间，默认120秒
- `poll_interval` (可选): 轮询间隔，默认2秒

**作用**: 同步生成图片，提交任务并轮询结果直到完成

#### 4.3 异步生成图片
**接口**: `POST /api/mj/generate-image`

**参数**:
- `prompt`: 图片生成提示词
- `img_file` (可选): 样貌参考图片

**作用**: 异步提交图片生成任务，立即返回任务ID

#### 4.4 异步提交生图任务
**接口**: `POST /api/mj/async/generate/image`

**参数**:
- `prompt`: 图片生成提示词
- `img_file` (可选): 样貌参考图片

#### 4.5 异步查询任务状态
**接口**: `GET /api/mj/async/query/status`

**参数**:
- `task_id`: 任务ID

**作用**: 查询异步图片生成任务的状态和结果

#### 4.6 获取图像描述
**接口**: `POST /api/mj/sync/img/describe`

**参数**:
- `image_url`: 图片URL地址
- `max_wait_time` (可选): 最大等待时间，默认120秒
- `poll_interval` (可选): 轮询间隔，默认2秒

**作用**: 根据图片URL反推生成该图片的提示词

#### 4.7 通过文件获取图片描述
**接口**: `POST /api/mj/sync/file/img/describe`

**参数**:
- `img_file`: 上传的图片文件
- `max_wait_time` (可选): 最大等待时间，默认120秒
- `poll_interval` (可选): 轮询间隔，默认2秒

**作用**: 通过上传图片文件反推生成提示词

---

### 5. 极梦视频生成模块

#### 5.1 生成视频 (基础版)
**接口**: `POST /api/jm/generate-video`

**参数**:
- `prompt`: 视频生成提示词
- `img_url`: 图片URL地址
- `duration` (可选): 视频时长(秒)，默认5
- `max_wait_time` (可选): 最大等待时间，默认300秒
- `poll_interval` (可选): 轮询间隔，默认5秒
- `model_type` (可选): 模型类型，支持lite/pro，默认lite

#### 5.2 同步生成视频v2
**接口**: `POST /api/jm/sync/generate/video`

**参数**:
- `prompt`: 视频生成提示词
- `img_file`: 上传的图片文件
- `duration` (可选): 视频时长(秒)，默认5
- `max_wait_time` (可选): 最大等待时间，默认300秒
- `poll_interval` (可选): 轮询间隔，默认5秒
- `model_type` (可选): 模型类型，默认lite

**作用**: 支持通过图片文件生成视频，同步返回结果

#### 5.3 异步生成视频
**接口**: `POST /api/jm/async/generate/video`

**参数**:
- `prompt`: 视频生成提示词
- `img_url` (可选): 图片URL地址
- `img_file` (可选): 上传的图片文件
- `duration` (可选): 视频时长(秒)，默认5
- `model_type` (可选): 模型类型，默认lite

**作用**: 异步提交视频生成任务

#### 5.4 异步查询视频状态
**接口**: `GET /api/jm/async/query/status`

**参数**:
- `task_id`: 任务ID

**作用**: 查询异步视频生成任务的状态和结果

---

### 6. 任务管理模块

#### 6.1 新版异步提交任务
**接口**: `POST /api/task/create/task`

**参数**: TaskRequest对象
- `task_type` (可选): 任务类型 (tea/chop/lady/vlog)
- `prompt`: 生成提示词
- `img_url` (可选): 参考图片URL
- `ar` (可选): 生成图片的长宽比，默认9:16

**作用**: 异步提交任务到Modal进行处理，立即返回任务ID

#### 6.2 新版本异步提交任务v2
**接口**: `POST /api/task/create/task/v2`

**参数**: 同上

**作用**: 任务管理的升级版本

#### 6.3 异步查询任务状态
**接口**: `GET /api/task/status/{task_id}`

**参数**:
- `task_id`: 任务ID

**作用**: 查询任务执行状态和结果

---

### 7. VEO视频生成模块

#### 7.1 异步提交视频生成 (已弃用)
**接口**: `POST /api/veo/submit`

**参数**: VideoRequest对象
- `prompt`: 视频生成提示词

**作用**: 仅支持文本到视频转换

#### 7.2 异步提交任务 (已弃用)
**接口**: `POST /api/veo/async/submit`

**参数**:
- `prompt`: 生成视频的提示词
- `img_file` (可选): 首帧参考图

**作用**: 支持文本到视频或文本+图片到视频

#### 7.3 同步生成视频 (已弃用)
**接口**: `POST /api/veo/sync/generate/video`

**参数**:
- `prompt`: 生成视频的提示词
- `img_file` (可选): 首帧参考图

#### 7.4 获取任务状态 (已弃用)
**接口**: `GET /api/veo/task/{task_id}`

**参数**:
- `task_id`: 任务ID

---

### 8. Hedra口型合成模块

#### 8.1 Hedra 2.0 版本

##### 提交口型合成任务
**接口**: `POST /api/302/hedra/v2/submit/task`

**参数**:
- `img_file`: 图片文件
- `audio_file`: 音频文件

**作用**: 提交口型合成任务，将音频与图片进行口型同步

##### 查询任务状态 (已弃用)
**接口**: `GET /api/302/hedra/v2/task/status`

**参数**:
- `task_id`: 任务ID

**状态**: 已弃用

##### 上传文件到Hedra服务器
**接口**: `POST /api/302/hedra/v2/upload`

**参数**:
- `local_file`: 待上传的文件，支持图片和音频

**作用**: 上传文件到Hedra服务器，仅支持image和audio格式

#### 8.2 Hedra 3.0 版本

##### 上传文件到Hedra平台
**接口**: `POST /api/302/hedra/v3/file/upload`

**参数**:
- `local_file`: 待上传的文件，支持音频、图片、视频、语音
- `purpose` (可选): 上传文件的用途，支持"image"、"audio"、"video"、"voice"，默认"image"

**作用**: 上传文件到Hedra平台，返回资源的ID

##### 异步提交任务
**接口**: `POST /api/302/hedra/v3/submit/task`

**参数**:
- `img_file`: 图片文件
- `audio_file`: 音频文件

**作用**: 异步提交口型合成任务

##### 查询任务状态
**接口**: `GET /api/302/hedra/v3/task/status`

**参数**:
- `task_id`: 任务ID

**作用**: 查询口型合成任务的执行状态和结果

---

### 9. 302AI服务集成模块

#### 9.1 302AI Midjourney图片生成

##### 异步提交生图任务
**接口**: `POST /api/302/mj/async/generate/image`

**参数**:
- `prompt`: 图片生成提示词
- `img_file` (可选): 样貌参考图片

##### 取消任务
**接口**: `POST /api/302/mj/task/cancel`

**参数**:
- `task_id`: 要取消的任务ID

##### 异步查询任务状态
**接口**: `GET /api/302/mj/async/query/status`

**参数**:
- `task_id`: 任务ID
- `task_type` (可选): 任务类型，image(生图)/describe(反推提示词)，默认image
- `cdn_flag` (可选): 是否CDN转换，默认false（CDN转换耗时）

##### 获取图像描述
**接口**: `POST /api/302/mj/sync/img/describe`

**参数**:
- `image_url`: 图片URL地址
- `max_wait_time` (可选): 最大等待时间，默认120秒
- `poll_interval` (可选): 轮询间隔，默认2秒

##### 通过文件获取生图提示词
**接口**: `POST /api/302/mj/sync/file/img/describe`

**参数**:
- `img_file`: 上传的图片文件

##### 同步生成图片
**接口**: `POST /api/302/mj/sync/image`

**参数**:
- `prompt`: 图片生成提示词
- `img_file` (可选): 样貌参考图片
- `max_wait_time` (可选): 最大等待时间，默认500秒
- `poll_interval` (可选): 轮询间隔，默认4秒

#### 9.2 302AI 极梦视频生成

##### 同步生成视频v2
**接口**: `POST /api/302/jm/sync/generate/video`

**参数**:
- `prompt`: 视频生成提示词
- `img_file`: 生成视频的图片文件
- `duration` (可选): 视频时长(秒)，默认5
- `max_wait_time` (可选): 最大等待时间，默认300秒
- `poll_interval` (可选): 轮询间隔，默认5秒
- `model_type` (可选): 模型类型，默认lite

##### 异步生成视频
**接口**: `POST /api/302/jm/async/generate/video`

**参数**:
- `prompt`: 视频生成提示词
- `img_url` (可选): 图片URL地址
- `img_file` (可选): 上传的图片文件
- `duration` (可选): 视频时长(秒)，默认5
- `model_type` (可选): 模型类型，默认lite

##### 异步查询视频状态
**接口**: `GET /api/302/jm/async/query/status`

**参数**:
- `task_id`: 任务ID

#### 9.3 302AI Midjourney视频生成

##### 异步提交生成视频任务
**接口**: `POST /api/302/mj/video/async/submit`

**参数**:
- `prompt`: 生成视频的提示词
- `img_file` (可选): 首帧参考图文件

##### 异步查询生成任务进度
**接口**: `POST /api/302/mj/video/async/task/status`

**参数**:
- `task_id`: 任务ID

##### 同步生成视频
**接口**: `POST /api/302/mj/video/sync/generate/video`

**参数**:
- `prompt`: 生成视频的提示词
- `img_file`: 首帧参考图文件
- `timeout` (可选): 超时时间，默认300秒
- `interval` (可选): 轮询间隔，默认3秒

#### 9.4 302AI VEO视频生成

##### 异步提交任务
**接口**: `POST /api/302/veo/video/async/submit`

**参数**:
- `prompt`: 生成视频的提示词
- `img_file` (可选): 首帧参考图

**作用**: 异步提交VEO视频生成任务

##### 同步生成视频
**接口**: `POST /api/302/veo/video/sync/generate/video`

**参数**:
- `prompt`: 生成视频的提示词
- `img_file` (可选): 首帧参考图
- `max_wait_time` (可选): 最大等待时间，默认500秒
- `interval` (可选): 轮询间隔，默认5秒

**作用**: 同步生成VEO视频，等待结果返回

##### 获取任务状态
**接口**: `GET /api/302/veo/video/task/{task_id}`

**参数**:
- `task_id`: 任务ID
- `img_mode` (可选): 图文到视频模式，默认false

**作用**: 查询VEO视频生成任务的状态和结果

---

### 11. 海螺API模块

#### 11.1 同步生成音频
**接口**: `POST /api/302/hl_router/sync/generate/speech`

**参数**:
- `text`: TTS文本内容
- `voice_id`: Voice ID
- `speed` (可选): 语速 [0.5, 2]，默认1.0
- `vol` (可选): 音量 (0,10]，默认1.0
- `emotion` (可选): 情感 ["happy", "sad", "angry", "fearful", "disgusted", "surprised", "calm"]

**作用**: 使用指定音色和参数生成语音

#### 11.2 查询克隆的音色ID
**接口**: `GET /api/302/hl_router/sync/get/voices`

**作用**: 查询可用的克隆音色ID列表，接口来自官方，302没有对应的中转接口

#### 11.3 上传素材到302ai
**接口**: `POST /api/302/hl_router/sync/file/upload`

**参数**:
- `audio_file`: 音频文件
- `purpose` (可选): 意图，默认voice_clone

**作用**: 上传音频文件用于声音复刻

#### 11.4 声音克隆
**接口**: `POST /api/302/hl_router/sync/voice/clone`

**参数**:
- `text`: 复刻的文本
- `model` (可选): 支持的模型，默认speech-02-hd
  - 可选: speech-02-hd, speech-02-turbo, speech-01-hd, speech-01-turbo
- `need_noise_reduction` (可选): 是否开启降噪，默认true
- `voice_id` (可选): 音色克隆voice_id
- `prefix` (可选): 音色voice_id前缀，默认BoWong-
- `audio_file` (可选): 参考音频文件

**作用**: 基于参考音频创建个性化音色

---

### 10. 聚合接口模块

#### 10.1 图片生成聚合接口

##### 获取支持的模型列表
**接口**: `GET /api/union/img/model/list`

**作用**: 获取所有支持的图片生成模型列表

##### 生图
**接口**: `POST /api/union/img/sync/generate/image`

**参数**:
- `model` (可选): 生图模型，默认midjourney-v7-t2i
- `prompt`: 生图提示词
- `img_file`: 参考图
- `aspect_ratio` (可选): 图片尺寸，默认9:16

**作用**: 统一的图片生成接口，支持多种模型
**参考文档**: https://doc.302.ai/286288228e0

#### 10.2 视频生成聚合接口

##### 获取支持的模型列表
**接口**: `GET /api/union/video/model/list`

**作用**: 获取所有支持的视频生成模型列表

##### 异步提交任务
**接口**: `POST /api/union/video/async/generate/video`

**参数**:
- `prompt`: 生图提示词
- `img_file`: 首帧图片
- `model` (可选): 生图模型，默认seedance_i2v
- `duration` (可选): 视频时长，默认5

##### 异步查询任务状态
**接口**: `GET /api/union/video/async/{task_id}/status`

**参数**:
- `task_id`: 任务ID

---

### 12. ComfyUI工作流模块

#### 12.1 获取运行节点
**接口**: `GET /api/comfy/fetch/running/node`

**参数**:
- `task_count` (可选): 运行任务的数目，默认1

**作用**: 根据任务数获取可用的运行节点

#### 12.2 异步提交任务
**接口**: `POST /api/comfy/async/submit/task`

**参数**:
- `prompt`: 工作流节点数据

**作用**: 异步提交ComfyUI工作流任务

#### 12.3 查询任务状态
**接口**: `GET /api/comfy/async/task/status`

**参数**:
- `task_id`: 任务ID

**作用**: 查询ComfyUI工作流任务的执行状态

#### 12.4 同步执行工作流
**接口**: `POST /api/comfy/sync/execute/workflow`

**参数**:
- `prompt`: 工作流JSON字符串

**作用**: 同步执行ComfyUI工作流，等待结果返回

---

## 接口组合调用关系与应用场景

### 场景1: 完整的图片生成流程

1. **提示词预审** → `GET /api/mj/prompt/check`
   - 检查提示词是否符合要求

2. **异步提交生图任务** → `POST /api/mj/async/generate/image`
   - 提交图片生成任务

3. **轮询任务状态** → `GET /api/mj/async/query/status`
   - 定期查询任务进度直到完成

4. **文件上传** → `POST /api/file/upload/s3`
   - 将生成的图片上传到云存储

### 场景2: 图片到视频的完整流程

1. **上传参考图片** → `POST /api/file/upload/s3`
   - 上传用户的参考图片

2. **提交视频生成任务** → `POST /api/jm/async/generate/video`
   - 使用图片和提示词生成视频

3. **查询任务状态** → `GET /api/jm/async/query/status`
   - 监控视频生成进度

4. **保存到模板** → `POST /api/template/create`
   - 将成功的配置保存为模板

### 场景3: 模板化批量生产

1. **获取模板列表** → `GET /api/template/default`
   - 获取可用的视频模板

2. **检查任务类型** → `GET /api/template/check/task_type`
   - 确认任务类型可用性

3. **批量任务提交** → `POST /api/task/create/task/v2`
   - 基于模板批量提交任务

4. **状态监控** → `GET /api/task/status/{task_id}`
   - 监控所有任务的执行状态

### 场景4: 多模型对比生成

1. **获取模型列表** → `GET /api/union/img/model/list`
   - 获取支持的图片生成模型

2. **并行提交任务** → `POST /api/union/img/sync/generate/image`
   - 使用不同模型生成同一提示词

3. **结果对比分析**
   - 比较不同模型的生成效果

### 场景5: 声音克隆与TTS

1. **上传音频素材** → `POST /api/302/hl_router/sync/file/upload`
   - 上传用于克隆的音频文件

2. **执行声音克隆** → `POST /api/302/hl_router/sync/voice/clone`
   - 创建个性化音色

3. **查询音色列表** → `GET /api/302/hl_router/sync/get/voices`
   - 获取可用的音色ID

4. **生成语音** → `POST /api/302/hl_router/sync/generate/speech`
   - 使用克隆的音色生成语音

### 场景6: 工作流自动化

1. **获取运行节点** → `GET /api/comfy/fetch/running/node`
   - 获取可用的ComfyUI节点

2. **提交工作流** → `POST /api/comfy/async/submit/task`
   - 提交复杂的AI处理工作流

3. **监控执行** → `GET /api/comfy/async/task/status`
   - 跟踪工作流执行状态

### 场景7: 图片反推与再生成

1. **上传图片** → `POST /api/mj/sync/file/img/describe`
   - 上传图片获取描述提示词

2. **优化提示词** → `GET /api/mj/prompt/check`
   - 检查和优化提示词

3. **重新生成** → `POST /api/mj/sync/image`
   - 基于优化后的提示词重新生成

### 场景8: 多平台视频生成对比

1. **准备素材** → `POST /api/file/upload/s3`
   - 上传参考图片

2. **并行生成**:
   - `POST /api/jm/sync/generate/video` (极梦)
   - `POST /api/302/mj/video/sync/generate/video` (302AI MJ)
   - `POST /api/302/veo/video/sync/generate/video` (302AI VEO)

3. **效果对比**
   - 比较不同平台的生成效果和速度

### 场景9: 口型合成与语音生成

1. **上传素材** → `POST /api/302/hedra/v3/file/upload`
   - 上传人物图片和音频文件

2. **声音克隆** → `POST /api/302/hl_router/sync/voice/clone`
   - 基于音频文件创建个性化音色

3. **生成语音** → `POST /api/302/hl_router/sync/generate/speech`
   - 使用克隆的音色生成新的语音

4. **口型合成** → `POST /api/302/hedra/v3/submit/task`
   - 将生成的语音与人物图片进行口型同步

5. **查询结果** → `GET /api/302/hedra/v3/task/status`
   - 获取最终的口型合成视频

### 场景10: 聚合接口多模型测试

1. **获取模型列表** → `GET /api/union/img/model/list`
   - 查看支持的图片生成模型

2. **批量测试** → `POST /api/union/img/sync/generate/image`
   - 使用不同模型生成同一提示词

3. **视频模型测试** → `GET /api/union/video/model/list`
   - 获取视频生成模型列表

4. **视频生成对比** → `POST /api/union/video/async/generate/video`
   - 使用不同视频模型进行对比测试

---

## 数据结构说明

### 通用响应格式

#### FileUploadResponse
```json
{
  "status": boolean,     // 上传状态
  "msg": string,         // 响应消息
  "data": string|null    // 文件URL
}
```

#### TaskRequest
```json
{
  "task_type": string|null,  // 任务类型 (tea/chop/lady/vlog)
  "prompt": string,          // 生成提示词
  "img_url": string|null,    // 参考图片URL
  "ar": string              // 长宽比，默认9:16
}
```

#### VideoRequest
```json
{
  "prompt": string  // 视频生成提示词
}
```

### 错误响应格式

#### HTTPValidationError
```json
{
  "detail": [
    {
      "loc": [string|integer],  // 错误位置
      "msg": string,            // 错误消息
      "type": string           // 错误类型
    }
  ]
}
```

---

## 最佳实践建议

### 1. 错误处理
- 所有接口都返回422状态码表示验证错误
- 建议实现重试机制，特别是对于网络相关的操作
- 异步任务应该实现超时处理
- 对于长时间运行的任务，建议设置合理的轮询间隔

### 2. 性能优化
- **文件上传**: 优先使用S3上传接口而非COS接口，支持CDN加速
- **任务处理**: 对于大批量任务，使用异步接口避免阻塞
- **轮询策略**: 合理设置轮询间隔，避免过于频繁的状态查询
- **并发控制**: 控制并发任务数量，避免系统过载

### 3. 资源管理
- 及时清理不需要的任务和文件
- 使用取消接口停止不需要的任务 (`POST /api/302/mj/task/cancel`)
- 合理配置超时时间，避免资源浪费
- 实现任务队列管理，优化资源利用

### 4. 安全考虑
- **文件验证**: 验证上传文件的类型和大小，特别是音频、图片、视频文件
- **内容审核**: 对用户输入的提示词进行预审，防止生成不当内容
- **访问控制**: 实现适当的访问控制和频率限制
- **数据保护**: 保护用户上传的文件和生成的内容，特别是声音克隆等敏感数据
- **隐私保护**: 对于声音克隆和口型合成等涉及个人特征的功能，需要特别注意隐私保护

### 5. 用户体验
- **进度反馈**: 为长时间运行的任务提供进度反馈
- **队列管理**: 实现任务队列管理，避免用户等待
- **历史记录**: 提供任务历史记录和结果缓存
- **错误提示**: 提供友好的错误提示和解决建议

### 6. 开发建议
- **接口选择**: 根据需求选择同步或异步接口
- **模型选择**: 根据质量要求选择合适的模型 (lite/pro)
- **参数调优**: 合理设置等待时间和轮询间隔
- **监控告警**: 实现任务监控和异常告警机制

---

## 总结

Text Video Agent API 提供了一个完整的AI内容生成生态系统，支持从简单的图片生成到复杂的多模态内容创作。通过合理的接口组合，可以实现丰富的应用场景，从个人创作工具到企业级内容生产平台。

### 核心优势

1. **多平台支持**: 集成了Midjourney、极梦、VEO、302AI、Hedra、海螺等多个AI服务提供商
2. **功能完整**: 涵盖图片生成、视频生成、音频生成、口型合成、声音克隆、工作流处理等全链路功能
3. **灵活调用**: 提供同步和异步两种调用模式，满足不同性能需求
4. **模板化**: 支持视频模板管理，便于批量生产和标准化流程
5. **聚合接口**: 提供统一的接口访问多种模型，简化开发复杂度
6. **多模态融合**: 支持图片、视频、音频的综合处理和口型合成

### 技术特点

- **RESTful设计**: 遵循现代API设计原则，接口清晰易用
- **异步处理**: 支持长时间运行的AI任务异步处理
- **文件管理**: 完善的文件上传和存储解决方案，支持多种文件格式
- **错误处理**: 统一的错误响应格式和验证机制
- **扩展性**: 模块化设计，便于功能扩展和维护
- **版本管理**: 支持API版本迭代，如Hedra 2.0/3.0等
- **多媒体支持**: 全面支持图片、音频、视频等多种媒体格式

### 新增功能亮点

1. **Hedra口型合成**: 支持2.0和3.0版本，提供高质量的口型同步功能
2. **海螺语音服务**: 集成专业的TTS和声音克隆功能
3. **聚合接口**: 统一多个AI服务提供商的接口，简化集成复杂度
4. **ComfyUI工作流**: 支持复杂的AI处理工作流
5. **302AI全家桶**: 提供Midjourney、极梦、VEO等服务的302AI版本

建议开发者根据具体应用场景选择合适的接口组合，并实现完善的错误处理和用户反馈机制，以提供最佳的用户体验。特别是在使用声音克隆和口型合成等功能时，需要注意隐私保护和合规使用。



{"id": "b647fb79-a9e4-451b-aae2-56afa1459998", "revision": 0, "last_node_id": 58, "last_link_id": 78, "nodes": [{"id": 27, "type": "Text Concatenate", "pos": [139.10777282714844, -151.3667755126953], "size": [270, 142], "flags": {}, "order": 15, "mode": 0, "inputs": [{"label": "文本_A", "name": "text_a", "shape": 7, "type": "STRING", "link": 64}, {"label": "文本_B", "name": "text_b", "shape": 7, "type": "STRING", "link": 65}, {"label": "文本_C", "name": "text_c", "shape": 7, "type": "STRING", "link": 63}, {"label": "文本_d", "name": "text_d", "shape": 7, "type": "STRING", "link": 75}], "outputs": [{"label": "字符串", "name": "STRING", "type": "STRING", "links": [36, 44]}], "properties": {"cnr_id": "was-ns", "ver": "3.0.0", "Node name for S&R": "Text Concatenate"}, "widgets_values": [" ", "true"]}, {"id": 37, "type": "easy showAnything", "pos": [-554.389892578125, 257.8910827636719], "size": [331.3696594238281, 367.22528076171875], "flags": {}, "order": 13, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 48}], "outputs": [{"name": "output", "type": "*", "links": [62]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.1", "Node name for S&R": "easy showAnything"}, "widgets_values": ["A person is dressed in a fashionable outfit consisting of a black sleeveless top paired with a fitted black skirt that features a white ruffled hemline. They are accessorized with white knee-high socks and bright red flat shoes, adding a pop of color to the ensemble. The individual is holding a handbag adorned with colorful decorations, including ribbons and small floral embellishments. Their stance is relaxed, with one hand resting near their hip and the other casually holding the bag.\nThe scene takes place in a modern setting, likely a boutique or showroom. The background showcases an industrial-style doorway with glass panels framed by black metal. Beyond the doorway, there is a display area with clothing hanging on racks, suggesting a fashionable retail environment. The space features neutral-toned walls and a gray carpeted floor, creating a sleek and understated atmosphere."]}, {"id": 40, "type": "S3UploadIMAGEURL", "pos": [-1049.9146728515625, -187.3993682861328], "size": [270, 58], "flags": {}, "order": 6, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 77}], "outputs": [{"label": "URL", "name": "URL", "type": "STRING", "links": [58, 64]}], "properties": {"aux_id": "ComfyUI-CustomNode.git", "ver": "bbea095da9854f8c0fe20f93254f1500f51eff1c", "Node name for S&R": "S3UploadIMAGEURL"}, "widgets_values": ["test"]}, {"id": 48, "type": "Note", "pos": [-1047.89697265625, 755.7965087890625], "size": [458.7157897949219, 251.05812072753906], "flags": {}, "order": 0, "mode": 0, "inputs": [], "outputs": [], "properties": {}, "widgets_values": ["不描述场景\n\nPlease provide a detailed description of this piece of clothing. Avoid describing any human body features such as skin color or skin marks. Additionally, ensure to avoid any prohibited words or phrases. Only output a description of the clothing.Describe clothes directly, starting with 'a...'\n\n**ALLOWED**\n- Any image up to PG-13 rating involving fiction, fantasy, mythology.\n- Real images that may be seen as respectful or light-hearted parodies, satire, caricatures\n- Imaginary or exaggerated real-life scenarios, including absurd or humorous situations.\n\n**NOT ALLOWED**\n- Disrespectful, harmful, misleading public figures/events portrayals or potential to mislead.\n- Hate speech, explicit or real-world violence.\n- Nudity or unconsented overtly sexualized public figures.\n- Imagery that might be considered culturally insensitive"], "color": "#432", "bgcolor": "#653"}, {"id": 50, "type": "easy imageScaleDownToSize", "pos": [-1925.8563232421875, 145.95310974121094], "size": [270, 82], "flags": {}, "order": 7, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 76}], "outputs": [{"label": "图像", "name": "IMAGE", "type": "IMAGE", "links": [69, 70, 71]}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.1", "Node name for S&R": "easy imageScaleDownToSize"}, "widgets_values": [512, true]}, {"id": 51, "type": "PreviewImage", "pos": [-1492.4449462890625, 359.886962890625], "size": [266.6930236816406, 364.6843566894531], "flags": {}, "order": 11, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 71}], "outputs": [], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "PreviewImage"}, "widgets_values": []}, {"id": 41, "type": "easy showAnything", "pos": [-737.2918701171875, -282.73724365234375], "size": [260.8580627441406, 88], "flags": {}, "order": 8, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 58}], "outputs": [{"name": "output", "type": "*", "links": []}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.1", "Node name for S&R": "easy showAnything"}, "widgets_values": ["https://cdn.roasmax.cn/test/tmpyg7hjk3v.png"]}, {"id": 11, "type": "LLMChatMultiModalImageTensor", "pos": [-1049.527099609375, 261.4039001464844], "size": [450.9809265136719, 384.0978088378906], "flags": {}, "order": 10, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 70}, {"name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 74}], "outputs": [{"label": "llm输出", "name": "llm输出", "type": "STRING", "links": [48]}], "properties": {"aux_id": "ComfyUI-CustomNode.git", "ver": "42f3db768c1413c101e6c55aea94b9794d8c85f2", "Node name for S&R": "LLMChatMultiModalImageTensor"}, "widgets_values": ["gpt-4o-1120", "", 0.7, 4096, 121]}, {"id": 35, "type": "S3UploadIMAGEURL", "pos": [-1079.114501953125, 9.697609901428223], "size": [270, 58], "flags": {}, "order": 9, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 69}], "outputs": [{"label": "URL", "name": "URL", "type": "STRING", "links": [45, 65]}], "properties": {"aux_id": "ComfyUI-CustomNode.git", "ver": "bbea095da9854f8c0fe20f93254f1500f51eff1c", "Node name for S&R": "S3UploadIMAGEURL"}, "widgets_values": ["test"]}, {"id": 36, "type": "easy showAnything", "pos": [-729.23046875, 92.5132827758789], "size": [268.3975830078125, 88], "flags": {}, "order": 12, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 45}], "outputs": [{"name": "output", "type": "*", "links": []}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.1", "Node name for S&R": "easy showAnything"}, "widgets_values": ["https://cdn.roasmax.cn/test/tmpsc7v2ywq.png"]}, {"id": 56, "type": "PrimitiveString", "pos": [-167.5723876953125, 84.83244323730469], "size": [270, 58], "flags": {}, "order": 1, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [75]}], "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "PrimitiveString"}, "widgets_values": [" --ar 9:16 --s 150 --v 7.0 --ow 800"]}, {"id": 34, "type": "ModalMidJourneyGenerateImage", "pos": [464.33251953125, -244.71023559570312], "size": [294.201416015625, 322.385498046875], "flags": {}, "order": 17, "mode": 0, "inputs": [{"label": "image", "name": "image", "type": "IMAGE", "link": 78}, {"label": "prompt", "name": "prompt", "type": "STRING", "widget": {"name": "prompt"}, "link": 44}], "outputs": [{"label": "image", "name": "image", "type": "IMAGE", "links": [52]}], "properties": {"aux_id": "ComfyUI-CustomNode.git", "ver": "bbea095da9854f8c0fe20f93254f1500f51eff1c", "Node name for S&R": "ModalMidJourneyGenerateImage"}, "widgets_values": ["一幅宏大壮美的山川画卷", "bowongai-test--text-video-agent-fastapi-app.modal.run", 240]}, {"id": 42, "type": "Text Concatenate", "pos": [-184.83560180664062, -124.04792022705078], "size": [270, 142], "flags": {}, "order": 14, "mode": 0, "inputs": [{"label": "文本_A", "name": "text_a", "shape": 7, "type": "STRING", "link": 67}, {"label": "文本_B", "name": "text_b", "shape": 7, "type": "STRING", "link": 62}, {"label": "文本_C", "name": "text_c", "shape": 7, "type": "STRING", "link": null}, {"label": "文本_d", "name": "text_d", "shape": 7, "type": "STRING", "link": null}], "outputs": [{"label": "字符串", "name": "STRING", "type": "STRING", "links": [63]}], "properties": {"cnr_id": "was-ns", "ver": "3.0.0", "Node name for S&R": "Text Concatenate"}, "widgets_values": [" ", "true"]}, {"id": 33, "type": "easy showAnything", "pos": [138.95408630371094, 65.10650634765625], "size": [276.2622375488281, 220.48167419433594], "flags": {}, "order": 16, "mode": 0, "inputs": [{"label": "输入任何", "name": "anything", "shape": 7, "type": "*", "link": 36}], "outputs": [{"name": "output", "type": "*", "links": null}], "properties": {"cnr_id": "comfyui-easy-use", "ver": "1.3.1", "Node name for S&R": "easy showAnything"}, "widgets_values": ["https://cdn.roasmax.cn/test/tmpyg7hjk3v.png https://cdn.roasmax.cn/test/tmpsc7v2ywq.png a 20-year-old fashion influencer with dark brown wavy hair.Plump and curvaceous figure. A person is dressed in a fashionable outfit consisting of a black sleeveless top paired with a fitted black skirt that features a white ruffled hemline. They are accessorized with white knee-high socks and bright red flat shoes, adding a pop of color to the ensemble. The individual is holding a handbag adorned with colorful decorations, including ribbons and small floral embellishments. Their stance is relaxed, with one hand resting near their hip and the other casually holding the bag.\nThe scene takes place in a modern setting, likely a boutique or showroom. The background showcases an industrial-style doorway with glass panels framed by black metal. Beyond the doorway, there is a display area with clothing hanging on racks, suggesting a fashionable retail environment. The space features neutral-toned walls and a gray carpeted floor, creating a sleek and understated atmosphere. --ar 9:16 --s 150 --v 7.0 --ow 800"]}, {"id": 58, "type": "LoadImgCustom", "pos": [-2323.911865234375, -570.9915161132812], "size": [288.9068298339844, 318], "flags": {}, "order": 2, "mode": 0, "inputs": [], "outputs": [{"name": "image", "type": "IMAGE", "links": [77, 78]}], "title": "BOWONG-INPUT-模特图", "properties": {"aux_id": "ComfyUI-CustomNode.git", "ver": "bbea095da9854f8c0fe20f93254f1500f51eff1c", "Node name for S&R": "LoadImgCustom"}, "widgets_values": ["https://example.com/sample.jpg", "20250711-131927.png", "image"]}, {"id": 45, "type": "String", "pos": [-2321.6103515625, -118.8946304321289], "size": [271.8282775878906, 216.02146911621094], "flags": {}, "order": 3, "mode": 0, "inputs": [], "outputs": [{"label": "字符串", "name": "STRING", "type": "STRING", "links": [67]}], "title": "BOWONG-INPUT-模特描述", "properties": {"cnr_id": "comfyui-logic", "ver": "1.0.0", "Node name for S&R": "String"}, "widgets_values": ["a 20-year-old fashion influencer with dark brown wavy hair.Plump and curvaceous figure."]}, {"id": 55, "type": "PrimitiveStringMultiline", "pos": [-2319.101318359375, 715.9893188476562], "size": [400, 200], "flags": {}, "order": 4, "mode": 0, "inputs": [], "outputs": [{"name": "STRING", "type": "STRING", "links": [74]}], "title": "提示词", "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "PrimitiveStringMultiline"}, "widgets_values": ["Please provide a detailed description of the clothing, actions of the person in the image, and the background of the scene. Avoid describing any human physical features, such as skin color or skin marks. Additionally, ensure that no prohibited words or phrases are used, and begin directly with \"A...\".\n\n**ALLOWED**\n- Any image up to PG-13 rating involving fiction, fantasy, mythology.\n- Real images that may be seen as respectful or light-hearted parodies, satire, caricatures\n- Imaginary or exaggerated real-life scenarios, including absurd or humorous situations.\n\n**NOT ALLOWED**\n- Disrespectful, harmful, misleading public figures/events portrayals or potential to mislead.\n- Hate speech, explicit or real-world violence.\n- Nudity or unconsented overtly sexualized public figures.\n- Imagery that might be considered culturally insensitive"]}, {"id": 39, "type": "SaveImage", "pos": [810.7241821289062, -163.19766235351562], "size": [1150.3494873046875, 1225.25634765625], "flags": {}, "order": 18, "mode": 0, "inputs": [{"label": "图像", "name": "images", "type": "IMAGE", "link": 52}], "outputs": [], "title": "BOWONG-OUTPUT", "properties": {"cnr_id": "comfy-core", "ver": "0.3.47", "Node name for S&R": "SaveImage"}, "widgets_values": ["MJ批量测试/0725"]}, {"id": 57, "type": "LoadImgCustom", "pos": [-2328.1865234375, 229.0241241455078], "size": [288.9068298339844, 318], "flags": {}, "order": 5, "mode": 0, "inputs": [], "outputs": [{"name": "image", "type": "IMAGE", "links": [76]}], "title": "BOWONG-INPUT-穿搭图", "properties": {"aux_id": "ComfyUI-CustomNode.git", "ver": "bbea095da9854f8c0fe20f93254f1500f51eff1c", "Node name for S&R": "LoadImgCustom"}, "widgets_values": ["https://example.com/sample.jpg", "20250711-131927.png", "image"]}], "links": [[36, 27, 0, 33, 0, "*"], [44, 27, 0, 34, 1, "STRING"], [45, 35, 0, 36, 0, "*"], [48, 11, 0, 37, 0, "*"], [52, 34, 0, 39, 0, "IMAGE"], [58, 40, 0, 41, 0, "*"], [62, 37, 0, 42, 1, "STRING"], [63, 42, 0, 27, 2, "STRING"], [64, 40, 0, 27, 0, "STRING"], [65, 35, 0, 27, 1, "STRING"], [67, 45, 0, 42, 0, "STRING"], [69, 50, 0, 35, 0, "IMAGE"], [70, 50, 0, 11, 0, "IMAGE"], [71, 50, 0, 51, 0, "IMAGE"], [74, 55, 0, 11, 1, "STRING"], [75, 56, 0, 27, 3, "STRING"], [76, 57, 0, 50, 0, "IMAGE"], [77, 58, 0, 40, 0, "IMAGE"], [78, 58, 0, 34, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.7247295000000148, "offset": [3546.4326713015143, 616.7631618357939]}, "frontendVersion": "1.23.4", "VHS_latentpreview": false, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4}
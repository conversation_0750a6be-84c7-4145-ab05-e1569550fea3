import {
  Code,
  Wrench,
  Database,
  FileSearch,
  Search,
  Sparkles,
  Heart,
  ArrowLeftRight,
  Image,
  Mic,
  Video,
  Wand2
} from 'lucide-react';
import { Tool, ToolCategory, ToolStatus } from '../types/tool';

/**
 * 工具数据配置
 * 定义所有可用的工具及其属性
 */
export const TOOLS_DATA: Tool[] = [
  {
    id: 'image-generation',
    name: 'AI图片生成工具',
    description: '基于先进AI技术的图片生成工具，支持提示词预审、参考图片和多种生成参数',
    longDescription: '专业的AI图片生成工具，集成Midjourney等先进的图像生成模型。支持提示词合规性预审、参考图片上传、多种画面比例和艺术风格选择。提供实时任务状态监控、异步生成处理、云存储自动上传等完整的图片生成流程。适用于创意设计、内容创作、艺术创作等多种场景。',
    icon: Image,
    route: '/tools/image-generation',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['AI生成', '图片创作', '提示词预审', '异步处理', '云存储'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-29'
  },
  {
    id: 'voice-clone',
    name: '声音克隆与TTS工具',
    description: '专业的语音合成和声音克隆工具，支持音频上传、声音克隆、音色管理和语音生成',
    longDescription: '先进的声音克隆与TTS工具，基于海螺API提供完整的语音合成和声音克隆功能。支持多种音频格式上传、个性化音色克隆、音色库管理、语音合成参数控制（语速、音量、情感等）。提供音频播放器和下载功能，适用于内容创作、配音制作、语音助手等多种场景。',
    icon: Mic,
    route: '/tools/voice-clone',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['声音克隆', 'TTS', '语音合成', '音频处理', '音色管理'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-29'
  },
  {
    id: 'similarity-search',
    name: '相似度检索工具',
    description: '基于AI的智能相似度搜索工具，支持多种相关性阈值和快速搜索功能',
    longDescription: '强大的AI驱动相似度检索工具，基于先进的机器学习算法提供精准的内容匹配。支持可调节的相关性阈值、智能搜索建议、实时结果展示和批量处理功能。适用于图像、文本和多媒体内容的相似性分析。',
    icon: Search,
    route: '/tools/similarity-search',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['AI搜索', '相似度检索', '智能匹配', '机器学习', '内容分析'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-25'
  },
  {
    id: 'outfit-recommendation',
    name: 'AI穿搭方案推荐',
    description: '基于TikTok视觉趋势的智能穿搭建议工具，提供个性化的时尚搭配方案',
    longDescription: '专业的AI穿搭顾问工具，基于TikTok视觉趋势和时尚潮流，为用户生成个性化的穿搭方案。支持多种风格选择、场合匹配、色彩搭配建议，并提供TikTok优化建议和拍摄技巧，助力内容创作和时尚搭配。',
    icon: Sparkles,
    route: '/tools/outfit-recommendation',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['AI穿搭', '时尚搭配', 'TikTok', '个性化推荐', '视觉趋势'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-25'
  },
  {
    id: 'outfit-search',
    name: '智能服装搜索',
    description: '基于AI的智能服装搜索工具，支持图像解析、相似度搜索和LLM问答',
    longDescription: '专业的服装搜索工具，基于先进的AI技术提供智能服装匹配和搜索功能。支持图像上传解析、多维度过滤搜索、相似度匹配、LLM智能问答等功能。提供直观的双列布局界面，左侧展示搜索结果，右侧提供搜索控制面板。',
    icon: Search,
    route: '/tools/outfit-search',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.BETA,
    tags: ['AI搜索', '服装匹配', '图像解析', 'LLM问答', '相似度搜索'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-26'
  },
  {
    id: 'outfit-favorites',
    name: '穿搭方案收藏管理',
    description: '管理收藏的穿搭方案，支持基于收藏方案的智能素材检索',
    longDescription: '专业的穿搭方案收藏管理工具，提供完整的收藏管理功能。支持收藏方案的添加、删除、搜索和筛选，以及基于收藏方案的智能素材检索功能。帮助用户建立个人穿搭方案库，快速找到适合的素材。',
    icon: Heart,
    route: '/tools/outfit-favorites',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['收藏管理', '穿搭方案', '素材检索', '个人库', '智能搜索'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-27'
  },
  {
    id: 'outfit-comparison',
    name: '穿搭方案对比分析',
    description: '分屏对比两个收藏方案的素材检索结果，分析方案差异',
    longDescription: '强大的穿搭方案对比分析工具，支持同时选择两个收藏方案进行分屏对比。并行执行素材检索，直观展示两个方案的检索结果差异，帮助用户更好地理解不同方案的特点和适用场景。',
    icon: ArrowLeftRight,
    route: '/tools/outfit-comparison',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['方案对比', '分屏展示', '差异分析', '素材检索', '对比分析'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-27'
  },
  {
    id: 'material-search',
    name: '智能素材检索',
    description: '基于收藏穿搭方案的智能素材检索工具，快速找到匹配的素材',
    longDescription: '专业的智能素材检索工具，基于收藏的穿搭方案进行精准的素材匹配。支持多维度检索条件生成、相关度排序、分页浏览等功能。提供直观的检索界面和丰富的筛选选项，帮助用户快速找到最适合的素材。',
    icon: Search,
    route: '/tools/material-search',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['素材检索', '智能匹配', '方案关联', '相关度排序', '精准搜索'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-27'
  },
  {
    id: 'volcano-video-generation',
    name: '火山云视频生成',
    description: '基于火山云API的智能视频生成工具，支持图片转视频、音频配音等功能',
    longDescription: '专业的火山云视频生成工具，集成火山云先进的视频生成API。支持图片转视频、音频配音、多种视频参数配置、实时进度监控等功能。提供直观的任务管理界面，支持批量操作、下载管理等完整的视频生成流程。适用于内容创作、营销推广、艺术创作等多种场景。',
    icon: Video,
    route: '/tools/volcano-video-generation',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['视频生成', '图片转视频', '音频配音', '火山云API', '批量处理'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-31'
  },
  {
    id: 'image-editing',
    name: '图像编辑工具',
    description: '基于火山云SeedEdit 3.0 API的智能图像编辑工具，支持单张和批量图片编辑',
    longDescription: '专业的AI图像编辑工具，集成火山云SeedEdit 3.0先进的图像编辑模型。支持通过提示词进行图像编辑、风格转换、场景变换等功能。提供单张图片编辑和批量处理模式，支持多种参数配置、实时进度监控、任务管理等完整的图像编辑流程。适用于创意设计、内容创作、图片处理等多种场景。',
    icon: Wand2,
    route: '/tools/image-editing',
    category: ToolCategory.AI_TOOLS,
    status: ToolStatus.STABLE,
    tags: ['图像编辑', 'AI编辑', '批量处理', '火山云API', '提示词编辑'],
    isNew: true,
    isPopular: true,
    version: '1.0.0',
    lastUpdated: '2024-01-31'
  }
];

/**
 * 根据ID获取工具信息
 */
export const getToolById = (id: string): Tool | undefined => {
  return TOOLS_DATA.find(tool => tool.id === id);
};

/**
 * 根据分类获取工具列表
 */
export const getToolsByCategory = (category: ToolCategory): Tool[] => {
  return TOOLS_DATA.filter(tool => tool.category === category);
};

/**
 * 获取热门工具列表
 */
export const getPopularTools = (): Tool[] => {
  return TOOLS_DATA.filter(tool => tool.isPopular);
};

/**
 * 获取新功能工具列表
 */
export const getNewTools = (): Tool[] => {
  return TOOLS_DATA.filter(tool => tool.isNew);
};

/**
 * 搜索工具
 */
export const searchTools = (query: string): Tool[] => {
  const lowercaseQuery = query.toLowerCase();
  return TOOLS_DATA.filter(tool => 
    tool.name.toLowerCase().includes(lowercaseQuery) ||
    tool.description.toLowerCase().includes(lowercaseQuery) ||
    tool.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
};

/**
 * 工具分类配置
 */
export const TOOL_CATEGORIES = [
  {
    id: ToolCategory.DATA_PROCESSING,
    name: '数据处理',
    description: '数据清洗、转换和处理工具',
    icon: Database,
    color: 'purple'
  },
  {
    id: ToolCategory.DEVELOPMENT,
    name: '开发调试',
    description: '开发和调试相关工具',
    icon: Code,
    color: 'indigo'
  },
  {
    id: ToolCategory.FILE_PROCESSING,
    name: '文件处理',
    description: '文件操作和处理工具',
    icon: FileSearch,
    color: 'orange'
  },
  {
    id: ToolCategory.AI_TOOLS,
    name: 'AI工具',
    description: '人工智能相关工具',
    icon: Wrench,
    color: 'pink'
  },
  {
    id: ToolCategory.UTILITIES,
    name: '实用工具',
    description: '通用实用工具集合',
    icon: Wrench,
    color: 'teal'
  }
];

/**
 * 获取分类配置
 */
export const getCategoryConfig = (category: ToolCategory) => {
  return TOOL_CATEGORIES.find(cat => cat.id === category);
};

-- 创建视频生成记录表
-- 遵循 Tauri 开发规范的数据库设计原则

CREATE TABLE IF NOT EXISTS video_generation_records (
    id TEXT PRIMARY KEY NOT NULL,
    project_id TEXT,
    name TEXT NOT NULL,
    description TEXT,
    
    -- 输入文件信息
    image_path TEXT,
    image_url TEXT,
    audio_path TEXT,
    audio_url TEXT,
    
    -- 生成参数
    prompt TEXT,
    negative_prompt TEXT,
    duration INTEGER DEFAULT 5, -- 视频时长（秒）
    fps INTEGER DEFAULT 24, -- 帧率
    resolution TEXT DEFAULT '1080p', -- 分辨率
    style TEXT, -- 风格参数
    motion_strength REAL DEFAULT 0.5, -- 运动强度 0.0-1.0
    
    -- 火山云API相关
    vol_task_id TEXT, -- 火山云任务ID
    vol_request_id TEXT, -- 火山云请求ID
    
    -- 生成状态和结果
    status TEXT NOT NULL DEFAULT 'Pending', -- Pending, Processing, Completed, Failed
    progress INTEGER DEFAULT 0, -- 进度百分比 0-100
    result_video_path TEXT,
    result_video_url TEXT,
    result_thumbnail_path TEXT,
    result_thumbnail_url TEXT,
    
    -- 错误信息
    error_message TEXT,
    error_code TEXT,
    
    -- 时间戳
    started_at INTEGER,
    completed_at INTEGER,
    generation_time_ms INTEGER,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    
    -- 外键约束
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_video_generation_records_project_id ON video_generation_records (project_id);
CREATE INDEX IF NOT EXISTS idx_video_generation_records_status ON video_generation_records (status);
CREATE INDEX IF NOT EXISTS idx_video_generation_records_created_at ON video_generation_records (created_at);
CREATE INDEX IF NOT EXISTS idx_video_generation_records_vol_task_id ON video_generation_records (vol_task_id);

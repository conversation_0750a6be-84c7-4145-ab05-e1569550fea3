# MixVideo UI/UX 设计规范

## 设计理念

### 核心原则
1. **用户至上**: 以用户体验为中心的设计
2. **简洁高效**: 简洁的界面，高效的操作流程
3. **一致性**: 统一的视觉语言和交互模式
4. **可访问性**: 支持无障碍访问和多设备适配
5. **现代化**: 符合现代设计趋势的视觉风格

### 设计目标
- **专业性**: 体现专业视频创作工具的品质
- **易用性**: 降低学习成本，提升操作效率
- **美观性**: 现代化的视觉设计，愉悦的用户体验
- **功能性**: 功能导向的界面布局和交互设计

## 视觉设计系统

### 颜色系统
```css
/* 主色调 - 现代蓝色系 */
:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;  /* 主品牌色 */
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  
  /* 中性色系 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* 语义色彩 */
  --success-500: #10b981;
  --warning-500: #f59e0b;
  --error-500: #ef4444;
  --info-500: #3b82f6;
}
```

### 颜色使用规范
- **主色**: 用于主要操作按钮、链接、重要状态指示
- **中性色**: 用于文本、边框、背景、次要元素
- **语义色**: 用于状态反馈、警告、错误、成功提示

### 字体系统
```css
/* 字体族 */
font-family: {
  sans: ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
  mono: ['JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', 'monospace']
}

/* 字体大小 */
fontSize: {
  xs: ['0.75rem', { lineHeight: '1rem' }],      /* 12px */
  sm: ['0.875rem', { lineHeight: '1.25rem' }],  /* 14px */
  base: ['1rem', { lineHeight: '1.5rem' }],     /* 16px */
  lg: ['1.125rem', { lineHeight: '1.75rem' }],  /* 18px */
  xl: ['1.25rem', { lineHeight: '1.75rem' }],   /* 20px */
  '2xl': ['1.5rem', { lineHeight: '2rem' }],    /* 24px */
  '3xl': ['1.875rem', { lineHeight: '2.25rem' }], /* 30px */
  '4xl': ['2.25rem', { lineHeight: '2.5rem' }],   /* 36px */
}
```

### 字体使用规范
- **标题**: 使用较大字号，font-weight: 600-700
- **正文**: 使用base字号，font-weight: 400
- **说明文字**: 使用sm字号，颜色为gray-600
- **代码**: 使用mono字体族

### 间距系统
```css
/* 基于8px网格的间距系统 */
spacing: {
  0: '0px',
  1: '0.25rem',  /* 4px */
  2: '0.5rem',   /* 8px */
  3: '0.75rem',  /* 12px */
  4: '1rem',     /* 16px */
  5: '1.25rem',  /* 20px */
  6: '1.5rem',   /* 24px */
  8: '2rem',     /* 32px */
  10: '2.5rem',  /* 40px */
  12: '3rem',    /* 48px */
  16: '4rem',    /* 64px */
  20: '5rem',    /* 80px */
  24: '6rem',    /* 96px */
}
```

### 圆角系统
```css
borderRadius: {
  none: '0',
  sm: '0.125rem',    /* 2px */
  DEFAULT: '0.25rem', /* 4px */
  md: '0.375rem',    /* 6px */
  lg: '0.5rem',      /* 8px */
  xl: '0.75rem',     /* 12px */
  '2xl': '1rem',     /* 16px */
  '3xl': '1.5rem',   /* 24px */
  full: '9999px',
}
```

### 阴影系统
```css
boxShadow: {
  xs: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  sm: '0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)',
  DEFAULT: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  md: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  lg: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  xl: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  glow: '0 0 20px rgb(59 130 246 / 0.15)',
}
```

## 组件设计规范

### 按钮设计
```typescript
// 按钮变体
const buttonVariants = {
  primary: 'bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500',
  secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
  outline: 'border border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-primary-500',
  ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',
  danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
};

// 按钮尺寸
const buttonSizes = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-base',
  lg: 'px-6 py-3 text-lg',
};

// 按钮状态
const buttonStates = {
  default: '',
  loading: 'opacity-75 cursor-not-allowed',
  disabled: 'opacity-50 cursor-not-allowed',
};
```

### 输入框设计
```typescript
const inputStyles = {
  base: 'block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500',
  error: 'border-red-300 text-red-900 placeholder-red-300 focus:border-red-500 focus:ring-red-500',
  disabled: 'bg-gray-50 text-gray-500 cursor-not-allowed',
};

const inputSizes = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-3 py-2 text-base',
  lg: 'px-4 py-3 text-lg',
};
```

### 卡片设计
```typescript
const cardStyles = {
  base: 'bg-white rounded-lg shadow-sm border border-gray-200',
  hover: 'hover:shadow-md transition-shadow duration-200',
  interactive: 'cursor-pointer hover:shadow-lg transform hover:-translate-y-1 transition-all duration-200',
  selected: 'ring-2 ring-primary-500 border-primary-500',
};
```

### 模态框设计
```typescript
const modalStyles = {
  overlay: 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50',
  container: 'bg-white rounded-lg shadow-xl max-w-md w-full max-h-screen overflow-y-auto',
  header: 'px-6 py-4 border-b border-gray-200',
  body: 'px-6 py-4',
  footer: 'px-6 py-4 border-t border-gray-200 flex justify-end space-x-3',
};
```

## 布局设计规范

### 网格系统
```css
/* 12列网格系统 */
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }
.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
/* ... */
.col-span-12 { grid-column: span 12 / span 12; }

/* 响应式断点 */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
```

### 页面布局
```typescript
// 主布局结构
const layoutStructure = {
  header: 'h-16 bg-white border-b border-gray-200',
  sidebar: 'w-64 bg-gray-50 border-r border-gray-200',
  main: 'flex-1 overflow-auto',
  content: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6',
};

// 侧边栏导航
const sidebarStyles = {
  nav: 'space-y-1 px-3 py-4',
  item: 'flex items-center px-3 py-2 text-sm font-medium rounded-md',
  active: 'bg-primary-100 text-primary-700',
  inactive: 'text-gray-600 hover:bg-gray-100 hover:text-gray-900',
};
```

### 内容区域
```typescript
const contentStyles = {
  section: 'mb-8',
  sectionHeader: 'mb-4',
  sectionTitle: 'text-2xl font-bold text-gray-900',
  sectionDescription: 'text-gray-600 mt-1',
  card: 'bg-white rounded-lg shadow-sm border border-gray-200 p-6',
};
```

## 交互设计规范

### 动画系统
```css
/* 过渡动画 */
.transition-all { transition: all 0.2s ease-in-out; }
.transition-colors { transition: color, background-color, border-color 0.2s ease-in-out; }
.transition-transform { transition: transform 0.2s ease-in-out; }

/* 进入动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

/* 加载动画 */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
```

### 交互状态
```typescript
const interactionStates = {
  hover: {
    button: 'hover:bg-opacity-90 hover:shadow-md',
    card: 'hover:shadow-lg hover:-translate-y-1',
    link: 'hover:text-primary-600 hover:underline',
  },
  focus: {
    input: 'focus:ring-2 focus:ring-primary-500 focus:border-primary-500',
    button: 'focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
  },
  active: {
    button: 'active:transform active:scale-95',
    tab: 'bg-primary-100 text-primary-700 border-primary-500',
  },
  disabled: {
    button: 'opacity-50 cursor-not-allowed',
    input: 'bg-gray-50 text-gray-500 cursor-not-allowed',
  },
};
```

### 反馈机制
```typescript
// 加载状态
const loadingStates = {
  spinner: 'animate-spin h-5 w-5 text-primary-600',
  skeleton: 'animate-pulse bg-gray-200 rounded',
  progress: 'bg-primary-600 h-2 rounded-full transition-all duration-300',
};

// 状态指示
const statusIndicators = {
  success: 'bg-green-100 text-green-800 border border-green-200',
  warning: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
  error: 'bg-red-100 text-red-800 border border-red-200',
  info: 'bg-blue-100 text-blue-800 border border-blue-200',
};

// 通知样式
const notificationStyles = {
  container: 'fixed top-4 right-4 z-50 space-y-2',
  item: 'bg-white rounded-lg shadow-lg border p-4 max-w-sm',
  success: 'border-l-4 border-green-500',
  error: 'border-l-4 border-red-500',
  warning: 'border-l-4 border-yellow-500',
  info: 'border-l-4 border-blue-500',
};
```

## 响应式设计规范

### 断点系统
```typescript
const breakpoints = {
  sm: '640px',   // 小屏幕手机
  md: '768px',   // 平板
  lg: '1024px',  // 小型桌面
  xl: '1280px',  // 桌面
  '2xl': '1536px', // 大屏桌面
};

// 响应式设计原则
const responsiveRules = {
  mobile: {
    // 移动端优先
    padding: 'px-4',
    fontSize: 'text-sm',
    spacing: 'space-y-4',
  },
  tablet: {
    // 平板适配
    padding: 'md:px-6',
    fontSize: 'md:text-base',
    spacing: 'md:space-y-6',
  },
  desktop: {
    // 桌面适配
    padding: 'lg:px-8',
    fontSize: 'lg:text-lg',
    spacing: 'lg:space-y-8',
  },
};
```

### 自适应布局
```typescript
// 网格自适应
const responsiveGrid = {
  mobile: 'grid-cols-1',
  tablet: 'md:grid-cols-2',
  desktop: 'lg:grid-cols-3 xl:grid-cols-4',
};

// 侧边栏响应式
const responsiveSidebar = {
  mobile: 'hidden', // 移动端隐藏
  tablet: 'md:block md:w-64', // 平板显示
  desktop: 'lg:w-72', // 桌面加宽
};
```

## 无障碍设计规范

### 键盘导航
```typescript
const keyboardNavigation = {
  focusable: 'focus:outline-none focus:ring-2 focus:ring-primary-500',
  skipLink: 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4',
  tabIndex: {
    interactive: '0',
    nonInteractive: '-1',
  },
};
```

### 屏幕阅读器支持
```typescript
const screenReaderSupport = {
  srOnly: 'sr-only', // 仅屏幕阅读器可见
  ariaLabel: 'aria-label', // 无障碍标签
  ariaDescribedBy: 'aria-describedby', // 描述关联
  role: 'role', // 元素角色
};
```

### 颜色对比度
```typescript
// 确保足够的颜色对比度
const contrastRatios = {
  normalText: '4.5:1', // 普通文本最小对比度
  largeText: '3:1',    // 大文本最小对比度
  uiComponents: '3:1', // UI组件最小对比度
};
```

## 性能优化规范

### 图片优化
```typescript
const imageOptimization = {
  formats: ['webp', 'avif', 'jpg', 'png'],
  sizes: {
    thumbnail: '150x150',
    small: '300x300',
    medium: '600x600',
    large: '1200x1200',
  },
  lazy: 'loading="lazy"',
  responsive: 'sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"',
};
```

### 动画性能
```css
/* 使用transform和opacity进行动画 */
.animate-optimized {
  will-change: transform, opacity;
  transform: translateZ(0); /* 启用硬件加速 */
}

/* 避免动画布局属性 */
.avoid-layout-animation {
  /* 避免动画这些属性：width, height, padding, margin, top, left */
}
```

### 虚拟化列表
```typescript
// 大列表虚拟化
const virtualizationConfig = {
  itemHeight: 80,
  containerHeight: 600,
  overscan: 5, // 预渲染项目数
  threshold: 100, // 启用虚拟化的最小项目数
};
```

## 设计审查清单

### 视觉设计
- [ ] 是否符合品牌色彩规范
- [ ] 字体大小和层次是否清晰
- [ ] 间距是否遵循8px网格系统
- [ ] 阴影和圆角是否一致
- [ ] 图标风格是否统一

### 交互设计
- [ ] 交互反馈是否及时明确
- [ ] 加载状态是否友好
- [ ] 错误提示是否清晰
- [ ] 操作流程是否简洁
- [ ] 快捷键是否合理

### 响应式设计
- [ ] 移动端适配是否完整
- [ ] 平板端布局是否合理
- [ ] 桌面端是否充分利用空间
- [ ] 断点切换是否平滑
- [ ] 触摸目标是否足够大

### 无障碍设计
- [ ] 颜色对比度是否符合标准
- [ ] 键盘导航是否完整
- [ ] 屏幕阅读器支持是否充分
- [ ] 焦点指示是否清晰
- [ ] 语义化标签是否正确

### 性能优化
- [ ] 图片是否优化
- [ ] 动画是否流畅
- [ ] 大列表是否虚拟化
- [ ] 资源是否懒加载
- [ ] 关键渲染路径是否优化

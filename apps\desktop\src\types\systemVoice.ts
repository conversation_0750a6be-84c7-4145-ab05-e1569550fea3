/**
 * 系统音色相关的类型定义
 */

// ============= 基础类型 =============

/**
 * 系统音色类型
 */
export enum SystemVoiceType {
  SYSTEM = 'system',
  PREMIUM = 'premium',
  CHILD = 'child',
  CHARACTER = 'character',
  HOLIDAY = 'holiday',
  ENGLISH = 'english',
}

/**
 * 音色性别
 */
export enum VoiceGender {
  MALE = 'male',
  FEMALE = 'female',
  CHILD = 'child',
  OTHER = 'other',
}

/**
 * 系统音色实体
 */
export interface SystemVoice {
  /** 唯一标识 */
  id: string;
  /** 音色ID */
  voice_id: string;
  /** 音色名称 */
  voice_name: string;
  /** 英文名称 */
  voice_name_en?: string;
  /** 描述 */
  description?: string;
  /** 音色类型 */
  voice_type: SystemVoiceType;
  /** 性别 */
  gender: VoiceGender;
  /** 语言 */
  language: string;
  /** 是否激活 */
  is_active: boolean;
  /** 排序顺序 */
  sort_order: number;
  /** 创建时间 */
  created_at: string;
  /** 更新时间 */
  updated_at: string;
}

/**
 * 系统音色查询请求
 */
export interface SystemVoiceQuery {
  /** 音色类型 */
  voice_type?: string;
  /** 性别 */
  gender?: string;
  /** 语言 */
  language?: string;
  /** 关键词搜索 */
  keyword?: string;
  /** 页码 */
  page?: number;
  /** 每页大小 */
  page_size?: number;
}

/**
 * 系统音色分页响应
 */
export interface SystemVoiceResponse {
  /** 音色列表 */
  voices: SystemVoice[];
  /** 总数 */
  total: number;
  /** 当前页 */
  page: number;
  /** 每页大小 */
  page_size: number;
  /** 总页数 */
  total_pages: number;
}

/**
 * 系统音色统计信息
 */
export interface SystemVoiceStats {
  /** 总数 */
  total: number;
  /** 按类型统计 */
  by_type: Record<string, number>;
  /** 按性别统计 */
  by_gender: Record<string, number>;
}

// ============= UI 相关类型 =============

/**
 * 音色选择器Props
 */
export interface VoiceSelectorProps {
  /** 当前选中的音色ID */
  selectedVoiceId?: string;
  /** 音色选择回调 */
  onVoiceSelect: (voiceId: string, voice: SystemVoice) => void;
  /** 是否显示搜索框 */
  showSearch?: boolean;
  /** 是否显示类型筛选 */
  showTypeFilter?: boolean;
  /** 是否显示性别筛选 */
  showGenderFilter?: boolean;
  /** 自定义样式类名 */
  className?: string;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 音色卡片Props
 */
export interface VoiceCardProps {
  /** 音色信息 */
  voice: SystemVoice;
  /** 是否选中 */
  isSelected: boolean;
  /** 点击回调 */
  onClick: () => void;
  /** 是否显示详细信息 */
  showDetails?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 音色类型标签Props
 */
export interface VoiceTypeBadgeProps {
  /** 音色类型 */
  type: SystemVoiceType;
  /** 大小 */
  size?: 'sm' | 'md' | 'lg';
  /** 自定义样式类名 */
  className?: string;
}

/**
 * 音色筛选器Props
 */
export interface VoiceFilterProps {
  /** 当前筛选条件 */
  filters: SystemVoiceQuery;
  /** 筛选条件变更回调 */
  onFiltersChange: (filters: SystemVoiceQuery) => void;
  /** 是否显示高级筛选 */
  showAdvanced?: boolean;
  /** 自定义样式类名 */
  className?: string;
}

// ============= 常量定义 =============

/**
 * 音色类型显示名称映射
 */
export const VOICE_TYPE_LABELS: Record<SystemVoiceType, string> = {
  [SystemVoiceType.SYSTEM]: '系统音色',
  [SystemVoiceType.PREMIUM]: '精品音色',
  [SystemVoiceType.CHILD]: '童声音色',
  [SystemVoiceType.CHARACTER]: '角色音色',
  [SystemVoiceType.HOLIDAY]: '节日音色',
  [SystemVoiceType.ENGLISH]: '英文音色',
};

/**
 * 性别显示名称映射
 */
export const GENDER_LABELS: Record<VoiceGender, string> = {
  [VoiceGender.MALE]: '男声',
  [VoiceGender.FEMALE]: '女声',
  [VoiceGender.CHILD]: '童声',
  [VoiceGender.OTHER]: '其他',
};

/**
 * 音色类型颜色映射
 */
export const VOICE_TYPE_COLORS: Record<SystemVoiceType, string> = {
  [SystemVoiceType.SYSTEM]: 'blue',
  [SystemVoiceType.PREMIUM]: 'purple',
  [SystemVoiceType.CHILD]: 'pink',
  [SystemVoiceType.CHARACTER]: 'green',
  [SystemVoiceType.HOLIDAY]: 'red',
  [SystemVoiceType.ENGLISH]: 'orange',
};

/**
 * 性别图标映射
 */
export const GENDER_ICONS: Record<VoiceGender, string> = {
  [VoiceGender.MALE]: '👨',
  [VoiceGender.FEMALE]: '👩',
  [VoiceGender.CHILD]: '👶',
  [VoiceGender.OTHER]: '🎭',
};

// ============= 工具函数类型 =============

/**
 * 音色筛选函数类型
 */
export type VoiceFilterFunction = (voice: SystemVoice, query: SystemVoiceQuery) => boolean;

/**
 * 音色排序函数类型
 */
export type VoiceSortFunction = (a: SystemVoice, b: SystemVoice) => number;

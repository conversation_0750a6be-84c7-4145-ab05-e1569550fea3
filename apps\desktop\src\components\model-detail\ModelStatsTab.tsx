import React from 'react';
import { Model } from '../../types/model';
import { ModelDashboardStats } from '../../types/outfitImage';
import { VideoGenerationTask, VideoGenerationStatus } from '../../types/videoGeneration';
import { OutfitImageRecord } from '../../types/outfitImage';

interface ModelStatsTabProps {
  model: Model;
  dashboardStats: ModelDashboardStats | null;
  videoTasks: VideoGenerationTask[];
  outfitRecords: OutfitImageRecord[];
}

/**
 * 模特详情数据统计Tab组件
 * 显示各种详细的统计信息
 */
export const ModelStatsTab: React.FC<ModelStatsTabProps> = ({
  model,
  dashboardStats,
  videoTasks,
  outfitRecords
}) => {
  return (
    <div className="animate-fade-in space-y-6">
      {/* 详细统计信息 */}
      {dashboardStats ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* 照片统计 */}
          <div className="bg-gradient-to-br from-white to-blue-50/30 rounded-2xl shadow-sm border border-gray-200/50 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">照片统计</h3>
              <span className="text-2xl">📸</span>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">总照片数</span>
                <span className="font-semibold text-blue-600">{model.photos.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">封面照片</span>
                <span className="font-semibold text-red-500">{model.photos.filter(p => p.is_cover).length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">普通照片</span>
                <span className="font-semibold text-gray-600">{model.photos.filter(p => !p.is_cover).length}</span>
              </div>
            </div>
          </div>

          {/* 视频统计 */}
          <div className="bg-gradient-to-br from-white to-red-50/30 rounded-2xl shadow-sm border border-gray-200/50 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">视频统计</h3>
              <span className="text-2xl">🎬</span>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">生成任务</span>
                <span className="font-semibold text-red-600">{videoTasks.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">已完成</span>
                <span className="font-semibold text-green-600">{videoTasks.filter(t => t.status === VideoGenerationStatus.Completed).length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">处理中</span>
                <span className="font-semibold text-blue-600">{videoTasks.filter(t => t.status === VideoGenerationStatus.Processing).length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">等待中</span>
                <span className="font-semibold text-yellow-600">{videoTasks.filter(t => t.status === VideoGenerationStatus.Pending).length}</span>
              </div>
            </div>
          </div>

          {/* 穿搭统计 */}
          <div className="bg-gradient-to-br from-white to-purple-50/30 rounded-2xl shadow-sm border border-gray-200/50 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">穿搭统计</h3>
              <span className="text-2xl">👗</span>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">生成记录</span>
                <span className="font-semibold text-purple-600">{outfitRecords.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">成功生成</span>
                <span className="font-semibold text-green-600">{outfitRecords.filter(r => r.status === 'completed').length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">处理中</span>
                <span className="font-semibold text-yellow-600">{outfitRecords.filter(r => r.status === 'processing').length}</span>
              </div>
            </div>
          </div>

          {/* 活动统计 */}
          <div className="bg-gradient-to-br from-white to-green-50/30 rounded-2xl shadow-sm border border-gray-200/50 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">活动统计</h3>
              <span className="text-2xl">📈</span>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">创建时间</span>
                <span className="font-semibold text-gray-600">{new Date(model.created_at).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">最后更新</span>
                <span className="font-semibold text-gray-600">{new Date(model.updated_at).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">标签数量</span>
                <span className="font-semibold text-green-600">{model.tags.length}</span>
              </div>
            </div>
          </div>

          {/* 文件统计 */}
          <div className="bg-gradient-to-br from-white to-indigo-50/30 rounded-2xl shadow-sm border border-gray-200/50 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">文件统计</h3>
              <span className="text-2xl">📁</span>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">照片文件</span>
                <span className="font-semibold text-indigo-600">{model.photos.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">视频文件</span>
                <span className="font-semibold text-indigo-600">
                  {videoTasks.filter(t => t.result?.video_paths && t.result.video_paths.length > 0).length}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">穿搭图片</span>
                <span className="font-semibold text-indigo-600">
                  {outfitRecords.filter(r => r.outfit_images && r.outfit_images.length > 0).length}
                </span>
              </div>
            </div>
          </div>

          {/* 性能统计 */}
          <div className="bg-gradient-to-br from-white to-yellow-50/30 rounded-2xl shadow-sm border border-gray-200/50 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">性能统计</h3>
              <span className="text-2xl">⚡</span>
            </div>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">成功率</span>
                <span className="font-semibold text-yellow-600">
                  {videoTasks.length > 0
                    ? `${Math.round((videoTasks.filter(t => t.status === VideoGenerationStatus.Completed).length / videoTasks.length) * 100)}%`
                    : '0%'
                  }
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">平均处理时间</span>
                <span className="font-semibold text-yellow-600">
                  {videoTasks.filter(t => t.status === VideoGenerationStatus.Completed).length > 0
                    ? '约 2-5 分钟'
                    : '暂无数据'
                  }
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">活跃度</span>
                <span className="font-semibold text-yellow-600">
                  {(videoTasks.length + outfitRecords.length) > 10 ? '高' :
                    (videoTasks.length + outfitRecords.length) > 5 ? '中' : '低'}
                </span>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="bg-gradient-to-br from-white to-primary-50/30 rounded-2xl shadow-sm border border-gray-200/50 p-6">
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">📈</span>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">加载统计数据中...</h3>
            <p className="text-gray-600">正在获取详细的统计信息</p>
          </div>
        </div>
      )}
    </div>
  );
};

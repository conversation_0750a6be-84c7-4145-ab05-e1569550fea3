# MixVideo 开发规范文档

本目录包含 MixVideo 项目的完整开发规范和指南，旨在确保代码质量、开发效率和团队协作的一致性。

## 📁 文档结构

### 核心规范文档

#### 1. [项目概览](./project-overview.md)
- 项目简介和技术架构
- 核心功能模块介绍
- 四层架构设计详解
- 开发环境配置
- 核心设计原则

#### 2. [前端开发规范](./frontend-coding-standards.md)
- React + TypeScript 开发规范
- 组件设计模式和最佳实践
- 状态管理 (Zustand) 规范
- 服务层设计原则
- 样式规范 (TailwindCSS)
- 自定义 Hooks 开发指南
- 测试策略和规范
- 性能优化技巧

#### 3. [后端开发规范](./backend-coding-standards.md)
- Rust + Tauri 开发规范
- 四层架构实现指南
- 数据模型设计原则
- 仓库模式实现
- 业务服务层设计
- Tauri 命令开发规范
- 错误处理策略
- 测试和性能优化

#### 4. [数据库设计规范](./database-design-standards.md)
- SQLite 数据库设计规范
- 表结构设计原则
- 索引优化策略
- 数据库迁移管理
- 查询性能优化
- 备份和恢复策略
- 安全和审计规范

#### 5. [UI/UX 设计规范](./ui-ux-design-standards.md)
- 视觉设计系统
- 组件设计规范
- 布局和响应式设计
- 交互设计原则
- 动画和过渡效果
- 无障碍设计指南
- 性能优化规范

#### 6. [开发指南](./development-guide.md)
- 快速开始指南
- 开发工作流程
- 核心开发模式
- 测试策略
- 调试技巧
- 性能优化
- 常见问题解决
- 部署指南

## 🎯 使用指南

### 新开发者入门
1. 首先阅读 [项目概览](./project-overview.md) 了解项目整体架构
2. 根据开发角色阅读对应的规范文档：
   - 前端开发者：[前端开发规范](./frontend-coding-standards.md)
   - 后端开发者：[后端开发规范](./backend-coding-standards.md)
   - 全栈开发者：阅读所有技术规范
3. 查看 [开发指南](./development-guide.md) 了解具体开发流程
4. 参考 [UI/UX 设计规范](./ui-ux-design-standards.md) 确保界面一致性

### 日常开发参考
- **开发新功能前**：查看相关规范确保设计符合项目标准
- **代码审查时**：使用规范文档中的检查清单
- **遇到问题时**：查看开发指南中的常见问题解决方案
- **性能优化时**：参考各规范文档中的性能优化章节

### 规范更新
- 规范文档会随着项目发展持续更新
- 重大变更会在团队会议中讨论
- 所有开发者都应关注规范文档的更新

## 🔧 技术栈概览

### 前端技术栈
- **框架**: React 18 + TypeScript 5.8
- **构建工具**: Vite 6.0
- **状态管理**: Zustand 4.4
- **UI框架**: TailwindCSS 3.4
- **图标**: Lucide React + Heroicons
- **路由**: React Router DOM 6.20
- **测试**: Vitest + Testing Library

### 后端技术栈
- **框架**: Tauri 2.0 + Rust 1.70+
- **数据库**: SQLite (WAL模式)
- **AI集成**: Google Gemini API
- **多媒体**: FFmpeg
- **异步**: Tokio + async/await
- **序列化**: Serde + JSON

### 开发工具
- **包管理**: PNPM 8.15 (Workspace)
- **代码检查**: ESLint + Clippy
- **格式化**: Prettier + rustfmt
- **版本控制**: Git + GitHub

## 📋 开发原则

### 1. 安全第一
- 严格的输入验证
- 安全的数据存储
- 防范常见安全漏洞
- 敏感信息保护

### 2. 性能优化
- 数据库查询优化
- 前端渲染优化
- 内存使用优化
- 网络请求优化

### 3. 用户体验
- 响应式设计
- 流畅的交互
- 清晰的反馈
- 无障碍支持

### 4. 代码质量
- 清晰的架构设计
- 完善的测试覆盖
- 详细的文档说明
- 一致的编码风格

### 5. 可维护性
- 模块化设计
- 松耦合架构
- 标准化接口
- 版本化管理

## 🚀 快速参考

### 常用命令
```bash
# 开发环境
pnpm tauri:dev

# 构建应用
pnpm tauri:build

# 运行测试
pnpm test

# 代码检查
pnpm lint

# 格式化代码
pnpm format
```

### 项目结构
```
mixvideo/
├── .promptx/           # 开发规范文档
├── apps/desktop/       # 主应用
│   ├── src/           # React前端
│   └── src-tauri/     # Rust后端
├── docs/              # 项目文档
└── packages/          # 共享包
```

### 重要链接
- [项目仓库](https://github.com/mixvideo/mixvideo)
- [问题追踪](https://github.com/mixvideo/mixvideo/issues)
- [发布记录](https://github.com/mixvideo/mixvideo/releases)
- [贡献指南](../CONTRIBUTING.md)

## 📞 支持和反馈

### 获取帮助
- **技术问题**: 在项目仓库创建 Issue
- **规范建议**: 通过 Pull Request 提交改进建议
- **团队讨论**: 参加定期的技术分享会议

### 贡献规范
1. Fork 项目仓库
2. 创建功能分支
3. 遵循开发规范
4. 提交 Pull Request
5. 通过代码审查

### 联系方式
- **项目负责人**: imeepos
- **技术讨论**: 项目 Discussions
- **紧急问题**: 项目 Issues

## 📝 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 完整的开发规范体系
- 四层架构设计指南
- 前后端开发规范
- UI/UX 设计系统

### 后续更新
- 规范文档会根据项目发展持续更新
- 重大变更会在版本发布说明中体现
- 建议定期查看文档更新

---

**注意**: 本规范文档是项目开发的重要参考，所有开发者都应该熟悉并遵循这些规范。如有疑问或建议，欢迎通过项目仓库进行讨论。

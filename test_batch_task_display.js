// 测试批量任务立即显示功能
// 这个脚本可以在浏览器控制台中运行来测试功能

async function testBatchTaskDisplay() {
    console.log('开始测试批量任务立即显示功能...');
    
    try {
        // 1. 获取初始任务列表
        console.log('1. 获取初始任务列表...');
        const initialTasks = await window.__TAURI__.core.invoke('get_all_batch_editing_tasks');
        console.log('初始批量任务数量:', initialTasks.length);
        
        // 2. 创建一个批量任务
        console.log('2. 创建批量任务...');
        const taskId = await window.__TAURI__.core.invoke('edit_batch_images', {
            inputFolder: 'C:\\Users\\<USER>\\Desktop\\test_images',
            outputFolder: 'C:\\Users\\<USER>\\Desktop\\test_output',
            prompt: '测试提示词',
            params: {
                guidance_scale: 5.5,
                seed: -1,
                watermark: false,
                response_format: 'url',
                size: 'adaptive'
            }
        });
        console.log('创建的任务ID:', taskId);
        
        // 3. 立即检查任务是否出现在列表中
        console.log('3. 立即检查任务列表...');
        const tasksAfterCreate = await window.__TAURI__.core.invoke('get_all_batch_editing_tasks');
        console.log('创建后批量任务数量:', tasksAfterCreate.length);
        
        const newTask = tasksAfterCreate.find(task => task.id === taskId);
        if (newTask) {
            console.log('✅ 成功！任务立即出现在列表中');
            console.log('任务状态:', newTask.status);
            console.log('任务进度:', newTask.progress);
            console.log('总图片数:', newTask.total_images);
            console.log('已处理图片数:', newTask.processed_images);
        } else {
            console.log('❌ 失败！任务没有立即出现在列表中');
        }
        
        // 4. 持续监控任务状态变化
        console.log('4. 开始监控任务状态变化...');
        let monitorCount = 0;
        const maxMonitorCount = 10;
        
        const monitor = setInterval(async () => {
            try {
                monitorCount++;
                const currentTasks = await window.__TAURI__.core.invoke('get_all_batch_editing_tasks');
                const currentTask = currentTasks.find(task => task.id === taskId);
                
                if (currentTask) {
                    console.log(`监控 ${monitorCount}: 状态=${currentTask.status}, 进度=${currentTask.progress}, 处理=${currentTask.processed_images}/${currentTask.total_images}`);
                    
                    // 如果任务完成或失败，停止监控
                    if (currentTask.status === 'Completed' || currentTask.status === 'Failed') {
                        console.log('任务已完成，停止监控');
                        clearInterval(monitor);
                    }
                } else {
                    console.log(`监控 ${monitorCount}: 任务不存在`);
                }
                
                // 最多监控10次
                if (monitorCount >= maxMonitorCount) {
                    console.log('达到最大监控次数，停止监控');
                    clearInterval(monitor);
                }
            } catch (error) {
                console.error('监控过程中出错:', error);
                clearInterval(monitor);
            }
        }, 2000); // 每2秒检查一次
        
    } catch (error) {
        console.error('测试过程中出错:', error);
    }
}

// 运行测试
console.log('批量任务显示测试脚本已加载');
console.log('运行 testBatchTaskDisplay() 开始测试');

import { describe, it, expect, vi } from 'vitest';
import { getImageSrc, isCloudUrl, isLocalPath } from '../imagePathUtils';

// Mock Tauri API
vi.mock('@tauri-apps/api/core', () => ({
  convertFileSrc: vi.fn((path: string) => `tauri://localhost${path}`)
}));

describe('imagePathUtils', () => {
  describe('isCloudUrl', () => {
    it('应该正确识别HTTPS URL', () => {
      expect(isCloudUrl('https://cdn.roasmax.cn/models/photos/model-1/image.jpg')).toBe(true);
    });

    it('应该正确识别HTTP URL', () => {
      expect(isCloudUrl('http://example.com/image.jpg')).toBe(true);
    });

    it('应该正确识别本地路径', () => {
      expect(isCloudUrl('/local/path/to/image.jpg')).toBe(false);
      expect(isCloudUrl('C:\\Windows\\path\\to\\image.jpg')).toBe(false);
      expect(isCloudUrl('./relative/path/image.jpg')).toBe(false);
    });
  });

  describe('isLocalPath', () => {
    it('应该正确识别本地路径', () => {
      expect(isLocalPath('/local/path/to/image.jpg')).toBe(true);
      expect(isLocalPath('C:\\Windows\\path\\to\\image.jpg')).toBe(true);
      expect(isLocalPath('./relative/path/image.jpg')).toBe(true);
    });

    it('应该正确识别云端URL', () => {
      expect(isLocalPath('https://cdn.roasmax.cn/models/photos/model-1/image.jpg')).toBe(false);
      expect(isLocalPath('http://example.com/image.jpg')).toBe(false);
    });
  });

  describe('getImageSrc', () => {
    it('应该直接返回HTTPS URL', () => {
      const cloudUrl = 'https://cdn.roasmax.cn/models/photos/model-1/image.jpg';
      expect(getImageSrc(cloudUrl)).toBe(cloudUrl);
    });

    it('应该直接返回HTTP URL', () => {
      const cloudUrl = 'http://example.com/image.jpg';
      expect(getImageSrc(cloudUrl)).toBe(cloudUrl);
    });

    it('应该转换本地路径', () => {
      const localPath = '/local/path/to/image.jpg';
      const result = getImageSrc(localPath);
      expect(result).toBe('tauri://localhost/local/path/to/image.jpg');
    });

    it('应该转换Windows本地路径', () => {
      const localPath = 'C:\\Windows\\path\\to\\image.jpg';
      const result = getImageSrc(localPath);
      expect(result).toBe('tauri://localhostC:\\Windows\\path\\to\\image.jpg');
    });

    it('应该转换相对路径', () => {
      const localPath = './relative/path/image.jpg';
      const result = getImageSrc(localPath);
      expect(result).toBe('tauri://localhost./relative/path/image.jpg');
    });
  });
});

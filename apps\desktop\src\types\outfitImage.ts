// 穿搭图片相关类型定义

export enum OutfitImageStatus {
  Pending = "pending",
  Processing = "processing",
  Completed = "completed",
  Failed = "failed"
}

export interface OutfitImageRecord {
  id: string;
  model_id: string;
  model_image_id: string; // 使用的模特形象图片ID
  generation_prompt?: string; // 生成提示词
  status: OutfitImageStatus;
  progress: number;
  result_urls: string[]; // 生成的穿搭图片URLs
  error_message?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  duration_ms?: number;
  comfyui_prompt_id?: string; // ComfyUI 任务ID，用于精确匹配任务状态更新
  generation_type?: string; // 生成方式: "standard" | "comfyui"
  comfyui_task_id?: string; // ComfyUI 任务ID

  // 关联数据
  product_images: ProductImage[];
  outfit_images: OutfitImage[];
}

export interface ProductImage {
  id: string;
  outfit_record_id: string;
  file_path: string;
  file_name: string;
  file_size: number;
  upload_url?: string; // 上传到云端的URL
  description?: string;
  created_at: string;
}

export interface OutfitImage {
  id: string;
  outfit_record_id: string;
  image_url: string;
  local_path?: string; // 本地缓存路径
  image_index: number; // 在生成结果中的索引
  description?: string;
  tags: string[];
  is_favorite: boolean;
  created_at: string;
}

export interface OutfitImageGenerationRequest {
  record_id?: string; // 指定要操作的记录ID（用于批量任务精确匹配）
  model_id: string;
  model_image_id: string; // 选择的模特形象图片ID
  product_image_paths: string[]; // 商品图片路径列表
  generation_prompt?: string; // 可选的生成提示词
  style_preferences?: string[]; // 风格偏好
  product_index?: number; // 商品编号（用于调试文件命名）
  use_comfyui?: boolean; // 是否使用 ComfyUI 生成
}

export interface OutfitImageGenerationResponse {
  record_id: string;
  generated_images: string[]; // 生成的图片URLs
  generation_time_ms: number;
  success: boolean;
  error_message?: string;
  generation_type?: string; // 生成方式: "standard" | "comfyui"
  task_id?: string; // ComfyUI 任务ID
}

export interface OutfitImageStats {
  total_records: number;
  total_images: number;
  favorite_images: number;
  pending_records: number;
  processing_records: number;
  completed_records: number;
  failed_records: number;
}

export interface OutfitImageRecordsResponse {
  records: OutfitImageRecord[];
  total_count: number;
  page: number;
  page_size: number;
  has_more: boolean;
}

// 模特个人看板统计信息
export interface ModelDashboardStats {
  // 基本信息
  model_id: string;
  model_name: string;
  
  // 照片统计
  total_photos: number;
  portrait_photos: number; // 个人形象照片
  work_photos: number; // 工作照片
  
  // 穿搭图片统计
  outfit_stats: OutfitImageStats;
  
  // 生成记录统计
  recent_generations: number; // 最近30天的生成次数
  success_rate: number; // 成功率 (0-1)
  
  // 其他统计
  favorite_count: number; // 收藏的穿搭图片数量
  total_generation_time_ms: number; // 总生成时间
  average_generation_time_ms: number; // 平均生成时间
}

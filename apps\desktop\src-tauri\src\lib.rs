// 导入宏
extern crate lazy_static;

// 四层架构模块定义
pub mod infrastructure;
pub mod data;
pub mod business;
pub mod presentation;
pub mod services;

// 应用状态和配置
pub mod app_state;
pub mod config;

// 集成测试模块
#[cfg(test)]
pub mod integration_tests;

use app_state::AppState;
use presentation::commands;
use tauri::Manager;
use infrastructure::logging;
use tracing::info;

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .manage(AppState::new())
        .manage(commands::tolerant_json_commands::JsonParserState::new())
        .manage(commands::markdown_commands::MarkdownParserState::new())
        .manage(commands::image_editing_commands::ImageEditingState::new())
        .invoke_handler(tauri::generate_handler![
            commands::project_commands::create_project,
            commands::project_commands::get_all_projects,
            commands::project_commands::get_project_by_id,
            commands::project_commands::update_project,
            commands::project_commands::delete_project,
            commands::project_commands::validate_project_path,
            commands::project_commands::get_default_project_name,
            commands::system_commands::select_directory,
            commands::system_commands::select_directory_with_options,
            commands::system_commands::select_file,
            commands::system_commands::select_file_with_options,
            commands::system_commands::save_file_with_options,
            commands::system_commands::open_file_directory,
            commands::system_commands::play_video_segment,
            commands::system_commands::get_app_info,
            commands::system_commands::validate_directory,
            commands::system_commands::get_directory_name,
            commands::system_commands::get_database_info,
            commands::system_commands::get_performance_report,
            commands::system_commands::cleanup_performance_data,
            commands::system_commands::record_performance_metric,
            commands::system_commands::cleanup_invalid_projects,
            commands::database_commands::initialize_database,
            commands::database_commands::check_database_connection,
            commands::database_commands::force_release_database_connection,
            commands::database_commands::force_run_database_migrations,
            commands::database_commands::get_connection_pool_stats,
            commands::database_commands::test_connection_pool_init,
            commands::database_commands::debug_database_data,
            commands::database_commands::get_detailed_connection_pool_stats,
            commands::database_commands::force_cleanup_database_connections,
            commands::material_commands::import_materials,
            commands::material_commands::import_materials_async,
            commands::material_commands::select_material_folders,
            commands::material_commands::scan_folder_materials,
            commands::material_commands::get_project_materials,
            commands::material_commands::get_all_materials,
            commands::material_commands::get_material_by_id,
            commands::material_commands::delete_material,
            commands::material_commands::batch_delete_materials,
            commands::material_commands::get_project_material_stats,
            commands::material_commands::batch_process_materials,
            commands::material_commands::update_material_status,
            commands::material_commands::cleanup_invalid_materials,
            commands::material_commands::is_supported_format,
            commands::material_commands::get_supported_extensions,
            commands::material_commands::select_material_files,
            commands::material_commands::validate_material_files,
            commands::material_commands::get_file_info,
            commands::material_commands::check_ffmpeg_available,
            commands::material_commands::get_ffmpeg_version,
            commands::material_commands::get_ffmpeg_status,
            commands::material_commands::extract_file_metadata,
            commands::material_commands::detect_video_scenes,
            commands::material_commands::generate_video_thumbnail,
            commands::material_commands::generate_and_save_segment_thumbnail,
            commands::material_commands::read_thumbnail_as_data_url,
            commands::material_commands::get_material_thumbnail_base64,
            commands::material_commands::get_segment_thumbnail_base64,
            commands::material_commands::get_audio_file_base64,
            commands::material_commands::test_scene_detection,
            commands::material_commands::get_material_segments,
            commands::material_commands::get_material_segment_by_id,
            commands::material_commands::test_video_split,
            commands::material_commands::associate_material_to_model,
            commands::material_commands::disassociate_material_from_model,
            commands::material_commands::get_materials_by_model_id,
            commands::material_commands::get_unassociated_materials,
            commands::material_commands::batch_bind_materials_to_model,
            commands::material_commands::batch_unbind_materials_from_model,
            commands::material_commands::switch_material_model,
            commands::material_commands::get_model_material_statistics,
            commands::material_commands::get_project_model_binding_stats,
            commands::material_commands::get_global_model_binding_stats,
            commands::material_commands::update_material,
            // 模特管理命令
            commands::model_commands::create_model,
            commands::model_commands::get_model_by_id,
            commands::model_commands::get_all_models,
            commands::model_commands::search_models,
            commands::model_commands::update_model,
            commands::model_commands::delete_model,
            commands::model_commands::delete_model_permanently,
            commands::model_commands::add_model_photo,
            commands::model_commands::delete_model_photo,
            commands::model_commands::set_cover_photo,
            commands::model_commands::add_model_tag,
            commands::model_commands::remove_model_tag,
            commands::model_commands::update_model_status,
            commands::model_commands::set_model_rating,
            commands::model_commands::set_model_avatar,
            commands::model_commands::get_model_statistics,
            commands::model_commands::select_photo_files,
            commands::model_commands::select_photo_file,
            // 模特动态管理命令
            commands::model_dynamic_commands::create_model_dynamic,
            commands::model_dynamic_commands::get_model_dynamic_by_id,
            commands::model_dynamic_commands::get_model_dynamics_by_model_id,
            commands::model_dynamic_commands::update_model_dynamic,
            commands::model_dynamic_commands::delete_model_dynamic,
            commands::model_dynamic_commands::get_model_dynamic_stats,
            commands::model_dynamic_commands::regenerate_dynamic_video,
            commands::model_dynamic_commands::get_available_ai_models,
            commands::model_dynamic_commands::update_video_generation_progress,
            commands::model_dynamic_commands::mark_video_generation_failed,
            // AI分类管理命令
            commands::ai_classification_commands::create_ai_classification,
            commands::ai_classification_commands::get_all_ai_classifications,
            commands::ai_classification_commands::get_ai_classification_by_id,
            commands::ai_classification_commands::update_ai_classification,
            commands::ai_classification_commands::delete_ai_classification,
            commands::ai_classification_commands::get_ai_classification_count,
            commands::ai_classification_commands::generate_ai_classification_preview,
            commands::ai_classification_commands::update_ai_classification_sort_orders,
            commands::ai_classification_commands::toggle_ai_classification_status,
            commands::ai_classification_commands::validate_ai_classification_name,
            // AI视频分类命令
            commands::video_classification_commands::start_video_classification,
            commands::video_classification_commands::start_project_batch_classification,
            commands::video_classification_commands::get_classification_queue_status,
            commands::video_classification_commands::get_project_classification_queue_status,
            commands::video_classification_commands::get_classification_task_progress,
            commands::video_classification_commands::get_all_classification_task_progress,
            commands::video_classification_commands::get_project_classification_task_progress,
            commands::video_classification_commands::stop_classification_queue,
            commands::video_classification_commands::recover_stuck_classification_tasks,
            commands::video_classification_commands::pause_classification_queue,
            commands::video_classification_commands::resume_classification_queue,
            commands::video_classification_commands::get_material_classification_records,
            commands::video_classification_commands::get_classification_statistics,
            commands::video_classification_commands::is_segment_classified,
            commands::video_classification_commands::cancel_classification_task,
            commands::video_classification_commands::retry_classification_task,
            commands::video_classification_commands::test_gemini_connection,
            // AI分析日志命令
            commands::ai_analysis_log_commands::get_ai_analysis_logs,
            commands::ai_analysis_log_commands::get_ai_analysis_stats,
            commands::ai_analysis_log_commands::export_ai_analysis_logs,
            commands::ai_analysis_log_commands::cleanup_ai_analysis_logs,
            commands::ai_analysis_log_commands::retry_failed_classification_task,
            commands::ai_analysis_log_commands::get_classification_record_detail,
            commands::ai_analysis_log_commands::delete_classification_records,
            commands::ai_analysis_log_commands::get_ai_analysis_log_filters,
            commands::ai_analysis_log_commands::create_test_ai_analysis_logs,
            // 模板管理命令
            commands::template_commands::list_templates,
            commands::template_commands::get_template_by_id,
            commands::template_commands::create_template,
            commands::template_commands::update_template,
            commands::template_commands::delete_template,
            commands::template_commands::hard_delete_template,
            commands::template_commands::verify_template_deletion,
            commands::template_commands::get_template_associations,
            commands::template_commands::import_template,
            commands::template_commands::get_import_progress,
            commands::template_commands::cancel_import,
            commands::template_commands::batch_import_templates,
            commands::template_commands::get_batch_import_progress,
            commands::template_commands::stop_batch_import,
            commands::template_commands::get_queue_status,
            commands::template_commands::clear_import_queue,
            commands::template_commands::validate_draft_file,
            commands::template_commands::preview_draft_file,
            commands::template_commands::scan_draft_files_count,
            commands::template_commands::get_template_performance_report,
            commands::template_commands::get_cache_stats,
            commands::template_commands::warm_template_cache,
            commands::template_commands::cleanup_template_performance_data,
            commands::template_commands::import_template_optimized,
            commands::template_commands::update_segment_matching_rule,
            commands::template_commands::get_segment_matching_rule,
            commands::template_commands::select_template_file,
            // 项目-模板绑定管理命令
            commands::project_template_binding_commands::create_project_template_binding,
            commands::project_template_binding_commands::update_project_template_binding,
            commands::project_template_binding_commands::delete_project_template_binding,
            commands::project_template_binding_commands::delete_project_template_binding_by_ids,
            commands::project_template_binding_commands::get_project_template_binding,
            commands::project_template_binding_commands::list_project_template_bindings,
            commands::project_template_binding_commands::get_templates_by_project,
            commands::project_template_binding_commands::get_projects_by_template,
            commands::project_template_binding_commands::batch_create_project_template_bindings,
            commands::project_template_binding_commands::batch_delete_project_template_bindings,
            commands::project_template_binding_commands::activate_project_template_binding,
            commands::project_template_binding_commands::deactivate_project_template_binding,
            commands::project_template_binding_commands::get_primary_template_binding_for_project,
            commands::project_template_binding_commands::set_primary_template_for_project,
            commands::project_template_binding_commands::check_project_template_binding_exists,
            // 素材匹配命令
            commands::material_matching_commands::execute_material_matching,
            commands::material_matching_commands::execute_material_matching_with_save,
            commands::material_matching_commands::get_project_material_stats_for_matching,
            commands::material_matching_commands::validate_template_binding_for_matching,
            commands::material_matching_commands::batch_match_all_templates,
            // 素材使用记录命令
            commands::material_usage_commands::create_material_usage_record,
            commands::material_usage_commands::create_material_usage_records_batch,
            commands::material_usage_commands::get_project_material_usage_records,
            commands::material_usage_commands::get_matching_result_usage_records,
            commands::material_usage_commands::get_project_material_usage_stats,
            commands::material_usage_commands::get_project_material_usage_overview,
            commands::material_usage_commands::create_usage_records_from_matching_result,
            commands::material_usage_commands::reset_material_segment_usage,
            commands::material_usage_commands::reset_project_material_usage,
            // 模板匹配结果命令
            commands::template_matching_result_commands::save_matching_result,
            commands::template_matching_result_commands::get_matching_result_detail,
            commands::template_matching_result_commands::get_project_matching_results,
            commands::template_matching_result_commands::get_template_matching_results,
            commands::template_matching_result_commands::get_binding_matching_results,
            commands::template_matching_result_commands::list_matching_results,
            commands::template_matching_result_commands::delete_matching_result,
            commands::template_matching_result_commands::soft_delete_matching_result,
            commands::template_matching_result_commands::soft_delete_matching_result_with_usage_reset,
            commands::template_matching_result_commands::batch_delete_matching_results,
            commands::template_matching_result_commands::batch_soft_delete_matching_results,
            commands::template_matching_result_commands::batch_delete_matching_results_with_usage_reset,
            commands::template_matching_result_commands::batch_soft_delete_matching_results_with_usage_reset,
            commands::template_matching_result_commands::reset_matching_result_export_status,
            commands::template_matching_result_commands::update_matching_result_info,
            commands::template_matching_result_commands::set_matching_result_quality_score,
            commands::template_matching_result_commands::get_matching_statistics,
            commands::template_matching_result_commands::create_matching_result,
            commands::template_matching_result_commands::get_matching_result_by_id,
            commands::template_matching_result_commands::get_matching_result_status_options,
            commands::template_matching_result_commands::export_matching_result_to_jianying,
            commands::template_matching_result_commands::export_matching_result_to_jianying_v2,
            commands::template_matching_result_commands::select_jianying_export_path,
            // 导出记录命令
            commands::export_record_commands::get_export_record,
            commands::export_record_commands::list_export_records,
            commands::export_record_commands::get_project_export_records,
            commands::export_record_commands::get_matching_result_export_records,
            commands::export_record_commands::delete_export_record,
            commands::export_record_commands::get_project_export_statistics,
            commands::export_record_commands::get_global_export_statistics,
            commands::export_record_commands::validate_export_file,
            commands::export_record_commands::cleanup_expired_export_records,
            commands::export_record_commands::re_export_record,
            // MaterialSegment聚合视图命令
            commands::material_segment_view_commands::get_project_segment_view,
            commands::material_segment_view_commands::get_project_segment_view_with_query,
            // 视频生成命令
            commands::video_generation_commands::create_video_generation_task,
            commands::video_generation_commands::execute_video_generation_task,
            commands::video_generation_commands::get_video_generation_task,
            commands::video_generation_commands::get_video_generation_tasks,
            commands::video_generation_commands::cancel_video_generation_task,
            commands::video_generation_commands::delete_video_generation_task,
            commands::video_generation_commands::retry_video_generation_task,
            commands::video_generation_commands::get_model_video_generation_statistics,
            commands::video_generation_commands::get_model_detail_with_photos,
            commands::video_generation_commands::batch_upload_model_photos,
            // 测试命令
            commands::test_commands::test_database_connection,
            commands::test_commands::test_template_table,
            commands::test_commands::test_logging,
            // 调试命令
            commands::debug_commands::test_parse_draft_file,
            commands::debug_commands::validate_template_structure,
            // 便捷工具命令
            commands::tools_commands::clean_jsonl_data,
            // 服装搭配搜索命令
            commands::outfit_search_commands::analyze_outfit_image,
            commands::outfit_search_commands::search_similar_outfits,
            commands::outfit_search_commands::ask_llm_outfit_advice,
            commands::outfit_search_commands::get_outfit_search_suggestions,
            commands::outfit_search_commands::generate_search_config_from_analysis,
            commands::outfit_search_commands::validate_outfit_image,
            commands::outfit_search_commands::get_supported_image_formats,
            commands::outfit_search_commands::get_default_search_config,
            commands::outfit_search_commands::get_outfit_search_config,
            commands::outfit_search_commands::generate_outfit_recommendations,
            commands::outfit_search_commands::enrich_analysis_result,
            // 素材库检索命令
            commands::material_search_commands::generate_material_search_query,
            commands::material_search_commands::search_materials_for_outfit,
            commands::material_search_commands::quick_material_search,
            // 穿搭方案收藏命令
            commands::outfit_favorite_commands::save_outfit_to_favorites,
            commands::outfit_favorite_commands::get_favorite_outfits,
            commands::outfit_favorite_commands::remove_from_favorites,
            commands::outfit_favorite_commands::search_materials_by_favorite,
            commands::outfit_favorite_commands::compare_outfit_favorites,
            commands::outfit_favorite_commands::is_outfit_favorited,
            // 相似度检索工具命令
            commands::similarity_search_commands::quick_similarity_search,
            commands::similarity_search_commands::get_similarity_search_suggestions,
            commands::similarity_search_commands::get_similarity_search_config,
            // 自定义标签管理命令
            commands::custom_tag_commands::get_custom_tag_categories,
            commands::custom_tag_commands::create_custom_tag_category,
            commands::custom_tag_commands::update_custom_tag_category,
            commands::custom_tag_commands::delete_custom_tag_category,
            commands::custom_tag_commands::get_custom_tags,
            commands::custom_tag_commands::create_custom_tag,
            commands::custom_tag_commands::update_custom_tag,
            commands::custom_tag_commands::delete_custom_tag,
            commands::custom_tag_commands::add_entity_tag,
            commands::custom_tag_commands::remove_entity_tag,
            commands::custom_tag_commands::get_entity_tags,
            commands::custom_tag_commands::get_tag_statistics,
            commands::custom_tag_commands::batch_add_entity_tags,
            commands::custom_tag_commands::batch_remove_entity_tags,
            // 容错JSON解析器命令
            commands::tolerant_json_commands::parse_json_tolerant,
            commands::tolerant_json_commands::validate_json_format,
            commands::tolerant_json_commands::format_json_text,
            commands::tolerant_json_commands::get_recovery_strategies,
            commands::tolerant_json_commands::get_default_parser_config,
            // RAG Grounding命令
            commands::rag_grounding_commands::query_rag_grounding,
            commands::rag_grounding_commands::test_rag_grounding_connection,
            commands::rag_grounding_commands::get_rag_grounding_config,
            // 多轮对话命令
            commands::conversation_commands::create_conversation_session,
            commands::conversation_commands::get_conversation_session,
            commands::conversation_commands::get_conversation_history,
            commands::conversation_commands::get_conversation_sessions,
            commands::conversation_commands::delete_conversation_session,
            commands::conversation_commands::add_conversation_message,
            commands::conversation_commands::process_multi_turn_conversation,
            commands::conversation_commands::get_conversation_stats,
            commands::conversation_commands::cleanup_expired_sessions,
            commands::conversation_commands::update_session_title,
            commands::conversation_commands::generate_session_summary,
            commands::image_download_commands::download_image_from_uri,
            commands::image_download_commands::get_default_download_directory,
            commands::image_download_commands::validate_image_uri,
            commands::image_download_commands::get_image_info,
            // Markdown解析器命令
            commands::markdown_commands::parse_markdown,
            commands::markdown_commands::query_markdown_nodes,
            commands::markdown_commands::find_markdown_node_at_position,
            commands::markdown_commands::extract_markdown_outline,
            commands::markdown_commands::extract_markdown_links,
            commands::markdown_commands::validate_markdown,
            // 水印处理命令
            commands::watermark_commands::detect_watermarks_in_video,
            commands::watermark_commands::detect_watermarks_in_image,
            commands::watermark_commands::remove_watermarks_from_video,
            commands::watermark_commands::remove_watermarks_from_image,
            commands::watermark_commands::add_watermark_to_video,
            commands::watermark_commands::add_watermark_to_image,
            commands::watermark_commands::start_batch_watermark_task,
            commands::watermark_commands::get_watermark_templates,
            commands::watermark_commands::upload_watermark_template,
            commands::watermark_commands::delete_watermark_template,
            commands::watermark_commands::get_batch_task_status,
            commands::watermark_commands::get_watermark_template_thumbnail,
            commands::watermark_commands::cancel_batch_task,
            // 批量缩略图生成命令
            commands::thumbnail_commands::start_batch_thumbnail_generation,
            commands::thumbnail_commands::scan_folder_and_generate_thumbnails,
            commands::thumbnail_commands::get_thumbnail_task_status,
            commands::thumbnail_commands::cancel_thumbnail_task,
            commands::thumbnail_commands::pause_thumbnail_task,
            commands::thumbnail_commands::resume_thumbnail_task,
            commands::thumbnail_commands::get_all_thumbnail_tasks,
            commands::thumbnail_commands::cleanup_completed_thumbnail_tasks,
            commands::thumbnail_commands::select_video_folder,
            commands::thumbnail_commands::scan_video_files,
            commands::thumbnail_commands::preview_thumbnail,
            // 模板片段权重配置命令
            commands::template_segment_weight_commands::create_template_segment_weight,
            commands::template_segment_weight_commands::get_segment_weights_with_defaults,
            commands::template_segment_weight_commands::get_classifications_by_segment_weight,
            commands::template_segment_weight_commands::batch_update_template_segment_weights,
            commands::template_segment_weight_commands::initialize_default_segment_weights,
            commands::template_segment_weight_commands::reset_segment_weights_to_global,
            commands::template_segment_weight_commands::get_template_weights,
            commands::template_segment_weight_commands::delete_template_weights,
            commands::template_segment_weight_commands::update_template_segment_weight,
            commands::template_segment_weight_commands::has_custom_segment_weights,
            commands::template_segment_weight_commands::get_template_weight_statistics,
            commands::template_segment_weight_commands::get_segment_weights_for_categories,
            // 目录设置管理命令
            commands::directory_settings_commands::get_directory_settings,
            commands::directory_settings_commands::get_directory_setting,
            commands::directory_settings_commands::update_directory_setting,
            commands::directory_settings_commands::batch_update_directory_settings,
            commands::directory_settings_commands::clear_directory_setting,
            commands::directory_settings_commands::reset_all_directory_settings,
            commands::directory_settings_commands::set_auto_remember_directories,
            commands::directory_settings_commands::is_auto_remember_enabled,
            commands::directory_settings_commands::validate_directory_path,
            commands::directory_settings_commands::select_and_save_directory,
            // 图片生成命令
            commands::image_generation_commands::check_image_prompt,
            commands::image_generation_commands::submit_image_generation_task,
            commands::image_generation_commands::query_image_generation_status,
            commands::image_generation_commands::upload_file_to_cloud,
            // 图片生成记录管理命令
            commands::image_generation_commands::create_image_generation_record,
            commands::image_generation_commands::start_image_generation_task,
            commands::image_generation_commands::update_image_generation_progress_by_task_id,
            commands::image_generation_commands::complete_image_generation_by_task_id,
            commands::image_generation_commands::fail_image_generation_by_task_id,
            commands::image_generation_commands::get_all_image_generation_records,
            commands::image_generation_commands::delete_image_generation_record,
            commands::image_generation_commands::start_background_task_monitoring,
            // 声音克隆与TTS命令
            commands::voice_clone_commands::upload_audio_file,
            commands::voice_clone_commands::clone_voice,
            commands::voice_clone_commands::get_voices,
            commands::voice_clone_commands::generate_speech,
            commands::voice_clone_commands::download_audio,
            commands::voice_clone_commands::download_audio_to_directory,
            commands::voice_clone_commands::batch_download_audio_to_directory,
            commands::voice_clone_commands::get_speech_generation_records,
            commands::voice_clone_commands::get_speech_generation_records_by_voice_id,
            // 系统音色命令
            commands::system_voice_commands::get_system_voices,
            commands::system_voice_commands::get_system_voices_by_type,
            commands::system_voice_commands::get_system_voices_by_gender,
            commands::system_voice_commands::get_system_voices_by_language,
            commands::system_voice_commands::search_system_voices,
            commands::system_voice_commands::get_system_voices_paginated,
            commands::system_voice_commands::get_system_voice_by_id,
            commands::system_voice_commands::check_system_voice_exists,
            commands::system_voice_commands::get_system_voice_stats,
            commands::voice_clone_commands::delete_speech_generation_record,
            // 穿搭图片相关命令
            commands::outfit_image_commands::get_model_dashboard_stats,
            commands::outfit_image_commands::get_outfit_image_records,
            commands::outfit_image_commands::get_outfit_image_records_paginated,
            commands::outfit_image_commands::create_outfit_image_record,
            commands::outfit_image_commands::delete_outfit_image_record,
            commands::outfit_image_commands::get_outfit_image_record_detail,
            commands::outfit_image_commands::execute_outfit_image_generation,
            commands::outfit_image_commands::execute_outfit_image_task,
            commands::outfit_image_commands::retry_outfit_image_generation,
            // 穿搭照片生成相关命令
            commands::outfit_photo_generation_commands::create_outfit_photo_generation_task,
            commands::outfit_photo_generation_commands::execute_outfit_photo_generation,
            commands::outfit_photo_generation_commands::get_comfyui_settings,
            commands::outfit_photo_generation_commands::update_comfyui_settings,
            commands::outfit_photo_generation_commands::test_comfyui_connection,
            // 工作流管理相关命令
            commands::workflow_management_commands::scan_workflows,
            commands::workflow_management_commands::validate_workflow_file,
            commands::workflow_management_commands::save_workflow_file,
            commands::workflow_management_commands::delete_workflow_file,
            commands::workflow_management_commands::copy_workflow_file,
            commands::workflow_management_commands::get_workflow_directory,
            commands::workflow_management_commands::set_workflow_directory,
            commands::workflow_management_commands::load_workflow_content,
            commands::workflow_management_commands::get_workflow_statistics,
            // 错误处理相关命令
            commands::error_handling_commands::get_error_statistics,
            commands::error_handling_commands::clear_error_history,
            commands::error_handling_commands::get_errors_by_category,
            commands::error_handling_commands::check_system_health,
            commands::error_handling_commands::handle_and_record_error,
            commands::error_handling_commands::get_error_suggestions,
            commands::error_handling_commands::check_error_retry_capability,
            commands::error_handling_commands::get_error_help_info,
            commands::error_handling_commands::export_error_report,
            commands::outfit_photo_generation_commands::get_outfit_photo_generation_history,
            commands::outfit_photo_generation_commands::delete_outfit_photo_generation,
            commands::outfit_photo_generation_commands::regenerate_outfit_photo,
            commands::outfit_photo_generation_commands::cancel_outfit_photo_generation,
            commands::outfit_photo_generation_commands::get_workflow_list,
            // 火山云视频生成相关命令
            commands::volcano_video_commands::create_volcano_video_generation,
            commands::volcano_video_commands::get_volcano_video_generations,
            commands::volcano_video_commands::get_volcano_video_generation_by_id,
            commands::volcano_video_commands::delete_volcano_video_generation,
            commands::volcano_video_commands::batch_delete_volcano_video_generations,
            commands::volcano_video_commands::download_volcano_video,
            commands::volcano_video_commands::download_video_to_directory,
            commands::volcano_video_commands::batch_download_volcano_videos,
            commands::volcano_video_commands::get_video_stream_base64,
            // 图像编辑命令
            commands::image_editing_commands::set_image_editing_config,
            commands::image_editing_commands::set_image_editing_api_key,
            commands::image_editing_commands::edit_single_image,
            commands::image_editing_commands::create_image_editing_task,
            commands::image_editing_commands::execute_image_editing_task,
            commands::image_editing_commands::get_image_editing_task_status,
            commands::image_editing_commands::edit_batch_images,
            commands::image_editing_commands::create_batch_editing_task,
            commands::image_editing_commands::get_batch_editing_task_status,
            commands::image_editing_commands::get_all_image_editing_tasks,
            commands::image_editing_commands::get_all_batch_editing_tasks,
            commands::image_editing_commands::clear_completed_tasks,
            commands::image_editing_commands::cancel_image_editing_task,
            // BowongTextVideoAgent 命令
            commands::bowong_text_video_agent_commands::initialize_bowong_service,
            commands::bowong_text_video_agent_commands::bowong_get_sample_prompt,
            commands::bowong_text_video_agent_commands::bowong_health_check,
            commands::bowong_text_video_agent_commands::bowong_upload_file_to_s3,
            commands::bowong_text_video_agent_commands::bowong_upload_file,
            commands::bowong_text_video_agent_commands::bowong_file_health_check,
            commands::bowong_text_video_agent_commands::bowong_get_templates,
            commands::bowong_text_video_agent_commands::bowong_get_template,
            commands::bowong_text_video_agent_commands::bowong_create_template,
            commands::bowong_text_video_agent_commands::bowong_update_template,
            commands::bowong_text_video_agent_commands::bowong_delete_template,
            commands::bowong_text_video_agent_commands::bowong_check_prompt,
            commands::bowong_text_video_agent_commands::bowong_sync_generate_image,
            commands::bowong_text_video_agent_commands::bowong_async_generate_image,
            commands::bowong_text_video_agent_commands::bowong_describe_image,
            commands::bowong_text_video_agent_commands::bowong_generate_video,
            commands::bowong_text_video_agent_commands::bowong_batch_query_video_status,
            commands::bowong_text_video_agent_commands::bowong_create_task,
            commands::bowong_text_video_agent_commands::bowong_get_task_status,
            commands::bowong_text_video_agent_commands::bowong_check_task_type,
            commands::bowong_text_video_agent_commands::bowong_ai302_mj_async_generate_image,
            commands::bowong_text_video_agent_commands::bowong_ai302_mj_cancel_task,
            commands::bowong_text_video_agent_commands::bowong_ai302_mj_query_task_status,
            commands::bowong_text_video_agent_commands::bowong_ai302_mj_sync_generate_image,
            commands::bowong_text_video_agent_commands::bowong_ai302_jm_sync_generate_video,
            commands::bowong_text_video_agent_commands::bowong_ai302_jm_async_generate_video,
            commands::bowong_text_video_agent_commands::bowong_ai302_veo_async_submit,
            commands::bowong_text_video_agent_commands::bowong_ai302_veo_sync_generate_video,
            commands::bowong_text_video_agent_commands::bowong_generate_speech,
            commands::bowong_text_video_agent_commands::bowong_get_voices,
            commands::bowong_text_video_agent_commands::bowong_upload_audio_file,
            commands::bowong_text_video_agent_commands::bowong_clone_voice,
            commands::bowong_text_video_agent_commands::bowong_get_image_model_list,
            commands::bowong_text_video_agent_commands::bowong_union_sync_generate_image,
            commands::bowong_text_video_agent_commands::bowong_get_video_model_list,
            commands::bowong_text_video_agent_commands::bowong_union_async_generate_video,
            commands::bowong_text_video_agent_commands::bowong_get_running_node,
            commands::bowong_text_video_agent_commands::bowong_submit_comfyui_task,
            commands::bowong_text_video_agent_commands::bowong_query_comfyui_task_status,
            commands::bowong_text_video_agent_commands::bowong_sync_execute_workflow,
            commands::bowong_text_video_agent_commands::bowong_cancel_tasks,
            commands::bowong_text_video_agent_commands::bowong_batch_query_task_status,
            commands::bowong_text_video_agent_commands::bowong_wait_for_task_completion
        ])
        .setup(|app| {
            // 初始化日志系统
            let mut log_config = logging::LogConfig::default();
            log_config.level = tracing::Level::INFO; // 设置为 INFO 级别以减少日志噪音
            if let Err(e) = logging::init_logging(log_config) {
                eprintln!("日志系统初始化失败: {}", e);
            }

            info!("MixVideo Desktop 应用启动");

            // 初始化应用状态
            let app_handle = app.handle();
            let state: tauri::State<AppState> = app_handle.state();

            // 初始化数据库
            if let Err(e) = state.initialize_database() {
                eprintln!("Failed to initialize database: {}", e);
                return Err(e.into());
            }

            // 同时管理 Database 实例供模板命令使用
            let database = state.get_database();
            app.manage(database);

            // 发布应用启动事件
            let event_bus_manager = state.event_bus_manager.clone();
            tauri::async_runtime::spawn(async move {
                let _ = event_bus_manager.publish_app_started().await;
            });

            // 记录启动性能指标
            {
                let mut monitor = state.performance_monitor.lock().unwrap();
                monitor.record_metric("startup_time_ms", 0.0); // 实际应该测量真实启动时间
            }

            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

#[cfg(test)]
mod tests {
    mod batch_delete_test;
    mod material_matching_service_test;
    mod batch_material_import_tests;
    mod priority_order_matching_tests;
    mod priority_order_matching_logic_test;
}



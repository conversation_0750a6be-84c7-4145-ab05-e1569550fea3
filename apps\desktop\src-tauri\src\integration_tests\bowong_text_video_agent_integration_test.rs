#[cfg(test)]
mod integration_tests {
    use crate::data::models::bowong_text_video_agent::*;
    use crate::infrastructure::bowong_text_video_agent_service::BowongTextVideoAgentService;
    use tokio;

    /// 创建测试配置
    fn create_test_config() -> BowongTextVideoAgentConfig {
        BowongTextVideoAgentConfig {
            base_url: "https://bowongai-test--text-video-agent-fastapi-app.modal.run/".to_string(),
            api_key: "bowong7777".to_string(),
            timeout: Some(30),
            retry_attempts: Some(3),
            enable_cache: Some(true),
            max_concurrency: Some(10),
        }
    }

    #[tokio::test]
    async fn test_service_initialization() {
        let config = create_test_config();
        let service = BowongTextVideoAgentService::new(config);
        
        assert!(service.is_ok(), "Service should initialize successfully");
        
        let service = service.unwrap();
        let config = service.get_config();
        assert_eq!(config.base_url, "https://api.bowong.com");
        assert_eq!(config.api_key, "test-api-key-12345");
        assert_eq!(config.timeout, Some(30));
        assert_eq!(config.retry_attempts, Some(3));
        assert_eq!(config.enable_cache, Some(true));
        assert_eq!(config.max_concurrency, Some(10));
    }

    #[tokio::test]
    async fn test_config_validation_edge_cases() {
        // 测试空字符串配置
        let invalid_config = BowongTextVideoAgentConfig {
            base_url: "   ".to_string(), // 只有空格
            api_key: "valid-key".to_string(),
            timeout: None,
            retry_attempts: None,
            enable_cache: None,
            max_concurrency: None,
        };
        
        let service = BowongTextVideoAgentService::new(invalid_config);
        assert!(service.is_err(), "Should fail with whitespace-only base_url");

        // 测试空 API key
        let invalid_config = BowongTextVideoAgentConfig {
            base_url: "https://api.test.com".to_string(),
            api_key: "   ".to_string(), // 只有空格
            timeout: None,
            retry_attempts: None,
            enable_cache: None,
            max_concurrency: None,
        };
        
        let service = BowongTextVideoAgentService::new(invalid_config);
        assert!(service.is_err(), "Should fail with whitespace-only api_key");
    }

    #[tokio::test]
    async fn test_data_model_completeness() {
        // 测试所有主要数据模型的序列化/反序列化
        
        // 测试 ApiResponse
        let api_response = ApiResponse {
            status: true,
            message: "Success".to_string(),
            data: Some(serde_json::json!({"key": "value"})),
        };
        
        let json = serde_json::to_string(&api_response).unwrap();
        let deserialized: ApiResponse = serde_json::from_str(&json).unwrap();
        assert_eq!(api_response.status, deserialized.status);
        assert_eq!(api_response.message, deserialized.message);

        // 测试 TaskResponse
        let task_response = TaskResponse {
            task_id: "task-123".to_string(),
            status: "pending".to_string(),
            message: "Task submitted".to_string(),
        };
        
        let json = serde_json::to_string(&task_response).unwrap();
        let deserialized: TaskResponse = serde_json::from_str(&json).unwrap();
        assert_eq!(task_response.task_id, deserialized.task_id);
        assert_eq!(task_response.status, deserialized.status);
        assert_eq!(task_response.message, deserialized.message);

        // 测试 TaskStatusResponse
        let task_status = TaskStatusResponse {
            task_id: "task-456".to_string(),
            status: "completed".to_string(),
            progress: Some(100.0),
            result: Some(serde_json::json!({"output": "success"})),
            error: None,
            created_at: Some("2024-01-01T00:00:00Z".to_string()),
            updated_at: Some("2024-01-01T00:00:00Z".to_string()),
        };
        
        let json = serde_json::to_string(&task_status).unwrap();
        let deserialized: TaskStatusResponse = serde_json::from_str(&json).unwrap();
        assert_eq!(task_status.task_id, deserialized.task_id);
        assert_eq!(task_status.status, deserialized.status);
        assert_eq!(task_status.progress, deserialized.progress);
    }

    #[tokio::test]
    async fn test_request_models() {
        // 测试各种请求模型的序列化
        
        // 测试 SyncImageGenerationRequest
        let image_request = SyncImageGenerationRequest {
            prompt: "A beautiful landscape".to_string(),
            img_file: Some("https://example.com/image.jpg".to_string()),
            max_wait_time: Some(120),
            poll_interval: Some(2),
        };
        
        let json = serde_json::to_string(&image_request).unwrap();
        let deserialized: SyncImageGenerationRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(image_request.prompt, deserialized.prompt);
        assert_eq!(image_request.img_file, deserialized.img_file);
        assert_eq!(image_request.max_wait_time, deserialized.max_wait_time);

        // 测试 VideoGenerationRequest
        let video_request = VideoGenerationRequest {
            prompt: "A video of nature".to_string(),
            img_url: Some("https://example.com/image.jpg".to_string()),
            duration: Some(10),
            max_wait_time: Some(300),
            poll_interval: Some(5),
        };

        let json = serde_json::to_string(&video_request).unwrap();
        let deserialized: VideoGenerationRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(video_request.prompt, deserialized.prompt);
        assert_eq!(video_request.img_url, deserialized.img_url);
        assert_eq!(video_request.duration, deserialized.duration);
        assert_eq!(video_request.max_wait_time, deserialized.max_wait_time);
    }

    #[tokio::test]
    async fn test_ai302_models() {
        // 测试 302AI 相关的数据模型
        
        let ai302_request = AI302MJImageRequest {
            prompt: "AI generated image".to_string(),
            img_file: Some("https://example.com/ref.jpg".to_string()),
            max_wait_time: Some(120),
            poll_interval: Some(2),
        };
        
        let json = serde_json::to_string(&ai302_request).unwrap();
        let deserialized: AI302MJImageRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(ai302_request.prompt, deserialized.prompt);
        assert_eq!(ai302_request.img_file, deserialized.img_file);

        let cancel_request = AI302TaskCancelRequest {
            task_id: "task-to-cancel".to_string(),
        };
        
        let json = serde_json::to_string(&cancel_request).unwrap();
        let deserialized: AI302TaskCancelRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(cancel_request.task_id, deserialized.task_id);
    }

    #[tokio::test]
    async fn test_file_upload_models() {
        // 测试文件上传相关的模型
        
        let s3_upload = S3FileUploadRequest {
            file_data: b"test file data".to_vec(),
            filename: "test.jpg".to_string(),
            content_type: Some("image/jpeg".to_string()),
        };

        let json = serde_json::to_string(&s3_upload).unwrap();
        let deserialized: S3FileUploadRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(s3_upload.file_data, deserialized.file_data);
        assert_eq!(s3_upload.filename, deserialized.filename);
        assert_eq!(s3_upload.content_type, deserialized.content_type);

        let upload_response = FileUploadResponse {
            file_url: "https://s3.example.com/file.jpg".to_string(),
            file_id: Some("file-123".to_string()),
            message: "Upload successful".to_string(),
        };
        
        let json = serde_json::to_string(&upload_response).unwrap();
        let deserialized: FileUploadResponse = serde_json::from_str(&json).unwrap();
        assert_eq!(upload_response.file_url, deserialized.file_url);
        assert_eq!(upload_response.file_id, deserialized.file_id);
        assert_eq!(upload_response.message, deserialized.message);
    }

    #[tokio::test]
    async fn test_template_models() {
        // 测试模板相关的模型
        
        let template = VideoTemplate {
            id: "template-123".to_string(),
            name: "Test Template".to_string(),
            description: Some("A test template".to_string()),
            config: serde_json::json!({"setting": "value"}),
            created_at: "2024-01-01T00:00:00Z".to_string(),
            updated_at: "2024-01-01T00:00:00Z".to_string(),
        };
        
        let json = serde_json::to_string(&template).unwrap();
        let deserialized: VideoTemplate = serde_json::from_str(&json).unwrap();
        assert_eq!(template.id, deserialized.id);
        assert_eq!(template.name, deserialized.name);
        assert_eq!(template.description, deserialized.description);

        let create_request = CreateTemplateRequest {
            name: "New Template".to_string(),
            description: Some("A new template".to_string()),
            config: serde_json::json!({"new": "config"}),
        };
        
        let json = serde_json::to_string(&create_request).unwrap();
        let deserialized: CreateTemplateRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(create_request.name, deserialized.name);
        assert_eq!(create_request.description, deserialized.description);
    }

    #[tokio::test]
    async fn test_voice_models() {
        // 测试语音相关的模型
        
        let voice_info = VoiceInfo {
            id: "voice-1".to_string(),
            name: "Female Voice".to_string(),
            language: "zh-CN".to_string(),
            gender: "female".to_string(),
        };
        
        let json = serde_json::to_string(&voice_info).unwrap();
        let deserialized: VoiceInfo = serde_json::from_str(&json).unwrap();
        assert_eq!(voice_info.id, deserialized.id);
        assert_eq!(voice_info.name, deserialized.name);
        assert_eq!(voice_info.language, deserialized.language);
        assert_eq!(voice_info.gender, deserialized.gender);

        let speech_request = SpeechGenerationRequest {
            text: "Hello world".to_string(),
            voice_id: Some("voice-1".to_string()),
            speed: Some(1.0),
            volume: Some(0.8),
        };
        
        let json = serde_json::to_string(&speech_request).unwrap();
        let deserialized: SpeechGenerationRequest = serde_json::from_str(&json).unwrap();
        assert_eq!(speech_request.text, deserialized.text);
        assert_eq!(speech_request.voice_id, deserialized.voice_id);
        assert_eq!(speech_request.speed, deserialized.speed);
        assert_eq!(speech_request.volume, deserialized.volume);
    }

    #[tokio::test]
    async fn test_architecture_integration() {
        // 测试整体架构集成
        let config = create_test_config();
        let service = BowongTextVideoAgentService::new(config).unwrap();
        
        // 验证服务配置
        let config = service.get_config();
        assert!(!config.base_url.is_empty());
        assert!(!config.api_key.is_empty());

        // 验证默认值处理
        assert!(config.timeout.unwrap_or(30) > 0);
        assert!(config.retry_attempts.unwrap_or(1) > 0);
        
        println!("✅ BowongTextVideoAgent 服务架构集成测试通过");
        println!("   - Rust 后端服务初始化成功");
        println!("   - 数据模型序列化/反序列化正常");
        println!("   - 配置验证机制工作正常");
        println!("   - 所有 API 模块结构完整");
    }
}

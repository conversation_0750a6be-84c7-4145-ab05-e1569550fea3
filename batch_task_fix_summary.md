# 图像编辑工具批量处理任务列表显示问题修复总结

## 问题描述
当用户点击批量处理按钮时，右侧边栏的"最近任务"列表显示为空。分析发现是因为后端的批量任务只在处理完成后才存储到任务列表中，而前端在任务开始时调用 loadTasks() 获取不到正在处理的任务。

## 修复方案

### 1. 修改后端批量编辑命令 (✅ 已完成)
**文件**: `apps/desktop/src-tauri/src/presentation/commands/image_editing_commands.rs`

**主要改动**:
- 在 `edit_batch_images` 函数开始时立即创建 `BatchImageEditingTask`
- 先将任务存储到 `state.batch_tasks` 中，状态设为 `Processing`
- 使用 `tokio::spawn` 异步执行批量处理，避免阻塞命令返回
- 通过回调机制实时更新任务状态
- 修复了生命周期问题，正确克隆 `Arc<Mutex<>>` 而不是借用 `State`

**关键代码变化**:
```rust
// 立即创建并存储任务
let mut batch_task = BatchImageEditingTask::new(/* ... */);
batch_task.status = ImageEditingTaskStatus::Processing;

// 立即存储，前端可以获取到
{
    let mut batch_tasks = state.batch_tasks.lock().map_err(/*...*/)?;
    batch_tasks.insert(task_id.clone(), batch_task.clone());
}

// 异步执行处理
tokio::spawn(async move {
    // 处理逻辑...
});
```

### 2. 修改图像编辑服务批量处理方法 (✅ 已完成)
**文件**: `apps/desktop/src-tauri/src/infrastructure/image_editing_service.rs`

**主要改动**:
- 添加新方法 `edit_batch_images_with_callback`
- 接受任务状态更新回调 `Box<dyn Fn(BatchImageEditingTask) + Send + Sync>`
- 在处理每个图片后调用回调更新任务状态
- 确保任务进度和状态能实时反映到存储中

**关键代码变化**:
```rust
pub async fn edit_batch_images_with_callback(
    &self,
    input_folder: &Path,
    output_folder: &Path,
    prompt: &str,
    params: &ImageEditingParams,
    task_update_callback: Option<Box<dyn Fn(BatchImageEditingTask) + Send + Sync>>,
) -> Result<BatchImageEditingTask> {
    // 初始回调
    if let Some(ref callback) = task_update_callback {
        callback(batch_task.clone());
    }
    
    // 处理每个图像后回调
    for (index, input_file) in image_files.iter().enumerate() {
        // ... 处理逻辑 ...
        batch_task.update_progress();
        
        // 每处理完一个图像就回调更新状态
        if let Some(ref callback) = task_update_callback {
            callback(batch_task.clone());
        }
    }
}
```

### 3. 优化前端任务刷新机制 (✅ 已完成)
**文件**: `apps/desktop/src/pages/tools/ImageEditingTool.tsx`

**主要改动**:
- 添加定时刷新状态管理 `refreshInterval`
- 实现 `hasProcessingTasks()` 检查是否有处理中的任务
- 实现 `startAutoRefresh()` 和 `stopAutoRefresh()` 控制定时刷新
- 使用 `useEffect` 监听任务状态变化，自动启动/停止刷新
- 简化批量处理函数，移除不必要的本地任务创建

**关键代码变化**:
```typescript
// 检查是否有处理中的任务
const hasProcessingTasks = useCallback(() => {
  return [...tasks, ...batchTasks].some(task => 
    task.status === ImageEditingTaskStatus.Processing || 
    task.status === ImageEditingTaskStatus.Pending
  );
}, [tasks, batchTasks]);

// 监听任务状态变化，自动启动/停止刷新
useEffect(() => {
  if (hasProcessingTasks()) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
}, [hasProcessingTasks, startAutoRefresh, stopAutoRefresh]);
```

## 修复效果

### ✅ 任务立即显示
- 用户点击批量处理后，右侧边栏立即显示新创建的批量任务
- 任务状态初始为 `Processing`

### ✅ 实时状态更新
- 任务状态、进度、已处理图片数量等信息实时更新
- 每2秒自动刷新任务列表（仅在有处理中任务时）

### ✅ 正确的任务生命周期
- 任务从 `Processing` 状态开始
- 处理完成后变为 `Completed` 或 `Failed`
- 自动停止不必要的刷新

### ✅ 技术要求满足
- **API兼容性**: 保持现有的前端调用方式不变
- **线程安全**: 使用 `Arc<Mutex<>>` 确保多线程环境下任务状态更新的安全性
- **错误处理**: 妥善处理任务创建、更新过程中的错误情况
- **性能考虑**: 只在有处理中任务时才启动定时刷新，避免不必要的性能开销

## 测试验证

创建了测试脚本 `test_batch_task_display.js`，可以在浏览器控制台中运行来验证：
1. 任务立即显示功能
2. 实时状态更新
3. 任务生命周期管理

## 编译状态
✅ Rust代码编译通过，无错误
✅ 应用可以正常启动和运行

## 下一步
建议进行完整的功能测试，包括：
1. 创建包含图片的测试文件夹
2. 测试批量处理功能
3. 验证任务列表实时更新
4. 确认任务完成后状态正确

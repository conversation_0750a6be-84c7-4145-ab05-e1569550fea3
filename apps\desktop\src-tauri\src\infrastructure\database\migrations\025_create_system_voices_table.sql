-- 创建系统音色表
CREATE TABLE IF NOT EXISTS system_voices (
    id TEXT PRIMARY KEY,
    voice_id TEXT NOT NULL UNIQUE,
    voice_name TEXT NOT NULL,
    voice_name_en TEXT,
    description TEXT,
    voice_type TEXT NOT NULL,
    gender TEXT NOT NULL,
    language TEXT NOT NULL DEFAULT 'zh-CN',
    is_active BOOLEAN NOT NULL DEFAULT 1,
    sort_order INTEGER NOT NULL DEFAULT 0,
    created_at TEXT NOT NULL,
    updated_at TEXT NOT NULL
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_system_voices_voice_id ON system_voices(voice_id);
CREATE INDEX IF NOT EXISTS idx_system_voices_type ON system_voices(voice_type);
CREATE INDEX IF NOT EXISTS idx_system_voices_active ON system_voices(is_active);
CREATE INDEX IF NOT EXISTS idx_system_voices_sort ON system_voices(sort_order);

-- 插入系统预设音色数据
INSERT OR IGNORE INTO system_voices (id, voice_id, voice_name, voice_name_en, description, voice_type, gender, language, is_active, sort_order, created_at, updated_at) VALUES
-- 基础系统音色
('sys_voice_001', 'male-qn-qingse', '青涩青年音色', 'Young Male Voice', '清新自然的青年男声', 'system', 'male', 'zh-CN', 1, 1, datetime('now'), datetime('now')),
('sys_voice_002', 'male-qn-jingying', '精英青年音色', 'Elite Male Voice', '成熟稳重的精英男声', 'system', 'male', 'zh-CN', 1, 2, datetime('now'), datetime('now')),
('sys_voice_003', 'male-qn-badao', '霸道青年音色', 'Dominant Male Voice', '霸气十足的青年男声', 'system', 'male', 'zh-CN', 1, 3, datetime('now'), datetime('now')),
('sys_voice_004', 'male-qn-daxuesheng', '青年大学生音色', 'College Student Voice', '阳光活力的大学生男声', 'system', 'male', 'zh-CN', 1, 4, datetime('now'), datetime('now')),
('sys_voice_005', 'female-shaonv', '少女音色', 'Young Girl Voice', '清纯甜美的少女声音', 'system', 'female', 'zh-CN', 1, 5, datetime('now'), datetime('now')),
('sys_voice_006', 'female-yujie', '御姐音色', 'Mature Lady Voice', '成熟魅力的御姐声音', 'system', 'female', 'zh-CN', 1, 6, datetime('now'), datetime('now')),
('sys_voice_007', 'female-chengshu', '成熟女性音色', 'Mature Woman Voice', '温柔知性的成熟女声', 'system', 'female', 'zh-CN', 1, 7, datetime('now'), datetime('now')),
('sys_voice_008', 'female-tianmei', '甜美女性音色', 'Sweet Female Voice', '甜美可爱的女性声音', 'system', 'female', 'zh-CN', 1, 8, datetime('now'), datetime('now')),

-- 主持人音色
('sys_voice_009', 'presenter_male', '男性主持人', 'Male Presenter', '专业的男性主持人声音', 'system', 'male', 'zh-CN', 1, 9, datetime('now'), datetime('now')),
('sys_voice_010', 'presenter_female', '女性主持人', 'Female Presenter', '专业的女性主持人声音', 'system', 'female', 'zh-CN', 1, 10, datetime('now'), datetime('now')),

-- 有声书音色
('sys_voice_011', 'audiobook_male_1', '男性有声书1', 'Male Audiobook 1', '适合有声书朗读的男声1', 'system', 'male', 'zh-CN', 1, 11, datetime('now'), datetime('now')),
('sys_voice_012', 'audiobook_male_2', '男性有声书2', 'Male Audiobook 2', '适合有声书朗读的男声2', 'system', 'male', 'zh-CN', 1, 12, datetime('now'), datetime('now')),
('sys_voice_013', 'audiobook_female_1', '女性有声书1', 'Female Audiobook 1', '适合有声书朗读的女声1', 'system', 'female', 'zh-CN', 1, 13, datetime('now'), datetime('now')),
('sys_voice_014', 'audiobook_female_2', '女性有声书2', 'Female Audiobook 2', '适合有声书朗读的女声2', 'system', 'female', 'zh-CN', 1, 14, datetime('now'), datetime('now')),

-- 精品音色 (Beta版本)
('sys_voice_015', 'male-qn-qingse-jingpin', '青涩青年音色-beta', 'Young Male Voice Beta', '精品版青涩青年男声', 'premium', 'male', 'zh-CN', 1, 15, datetime('now'), datetime('now')),
('sys_voice_016', 'male-qn-jingying-jingpin', '精英青年音色-beta', 'Elite Male Voice Beta', '精品版精英青年男声', 'premium', 'male', 'zh-CN', 1, 16, datetime('now'), datetime('now')),
('sys_voice_017', 'male-qn-badao-jingpin', '霸道青年音色-beta', 'Dominant Male Voice Beta', '精品版霸道青年男声', 'premium', 'male', 'zh-CN', 1, 17, datetime('now'), datetime('now')),
('sys_voice_018', 'male-qn-daxuesheng-jingpin', '青年大学生音色-beta', 'College Student Voice Beta', '精品版大学生男声', 'premium', 'male', 'zh-CN', 1, 18, datetime('now'), datetime('now')),
('sys_voice_019', 'female-shaonv-jingpin', '少女音色-beta', 'Young Girl Voice Beta', '精品版少女声音', 'premium', 'female', 'zh-CN', 1, 19, datetime('now'), datetime('now')),
('sys_voice_020', 'female-yujie-jingpin', '御姐音色-beta', 'Mature Lady Voice Beta', '精品版御姐声音', 'premium', 'female', 'zh-CN', 1, 20, datetime('now'), datetime('now')),
('sys_voice_021', 'female-chengshu-jingpin', '成熟女性音色-beta', 'Mature Woman Voice Beta', '精品版成熟女声', 'premium', 'female', 'zh-CN', 1, 21, datetime('now'), datetime('now')),
('sys_voice_022', 'female-tianmei-jingpin', '甜美女性音色-beta', 'Sweet Female Voice Beta', '精品版甜美女声', 'premium', 'female', 'zh-CN', 1, 22, datetime('now'), datetime('now')),

-- 童声音色
('sys_voice_023', 'clever_boy', '聪明男童', 'Clever Boy', '聪明活泼的男童声音', 'child', 'child', 'zh-CN', 1, 23, datetime('now'), datetime('now')),
('sys_voice_024', 'cute_boy', '可爱男童', 'Cute Boy', '可爱天真的男童声音', 'child', 'child', 'zh-CN', 1, 24, datetime('now'), datetime('now')),
('sys_voice_025', 'lovely_girl', '萌萌女童', 'Lovely Girl', '萌萌可爱的女童声音', 'child', 'child', 'zh-CN', 1, 25, datetime('now'), datetime('now')),
('sys_voice_026', 'cartoon_pig', '卡通猪小琪', 'Cartoon Pig', '卡通角色猪小琪的声音', 'child', 'other', 'zh-CN', 1, 26, datetime('now'), datetime('now')),

-- 角色音色
('sys_voice_027', 'bingjiao_didi', '病娇弟弟', 'Yandere Brother', '病娇风格的弟弟角色声音', 'character', 'male', 'zh-CN', 1, 27, datetime('now'), datetime('now')),
('sys_voice_028', 'junlang_nanyou', '俊朗男友', 'Handsome Boyfriend', '俊朗帅气的男友角色声音', 'character', 'male', 'zh-CN', 1, 28, datetime('now'), datetime('now')),
('sys_voice_029', 'chunzhen_xuedi', '纯真学弟', 'Innocent Junior', '纯真可爱的学弟角色声音', 'character', 'male', 'zh-CN', 1, 29, datetime('now'), datetime('now')),
('sys_voice_030', 'lengdan_xiongzhang', '冷淡学长', 'Cold Senior', '冷淡高冷的学长角色声音', 'character', 'male', 'zh-CN', 1, 30, datetime('now'), datetime('now')),
('sys_voice_031', 'badao_shaoye', '霸道少爷', 'Domineering Young Master', '霸道总裁风格的少爷声音', 'character', 'male', 'zh-CN', 1, 31, datetime('now'), datetime('now')),
('sys_voice_032', 'tianxin_xiaoling', '甜心小玲', 'Sweet Xiaoling', '甜心可爱的小玲角色声音', 'character', 'female', 'zh-CN', 1, 32, datetime('now'), datetime('now')),
('sys_voice_033', 'qiaopi_mengmei', '俏皮萌妹', 'Playful Cute Girl', '俏皮可爱的萌妹角色声音', 'character', 'female', 'zh-CN', 1, 33, datetime('now'), datetime('now')),
('sys_voice_034', 'wumei_yujie', '妩媚御姐', 'Charming Lady', '妩媚性感的御姐角色声音', 'character', 'female', 'zh-CN', 1, 34, datetime('now'), datetime('now')),
('sys_voice_035', 'diadia_xuemei', '嗲嗲学妹', 'Sweet Junior Girl', '嗲嗲可爱的学妹角色声音', 'character', 'female', 'zh-CN', 1, 35, datetime('now'), datetime('now')),
('sys_voice_036', 'danya_xuejie', '淡雅学姐', 'Elegant Senior Girl', '淡雅知性的学姐角色声音', 'character', 'female', 'zh-CN', 1, 36, datetime('now'), datetime('now')),

-- 节日音色
('sys_voice_037', 'Santa_Claus', 'Santa Claus', 'Santa Claus', '圣诞老人的经典声音', 'holiday', 'male', 'en-US', 1, 37, datetime('now'), datetime('now')),
('sys_voice_038', 'Grinch', 'Grinch', 'Grinch', '圣诞怪杰格林奇的声音', 'holiday', 'male', 'en-US', 1, 38, datetime('now'), datetime('now')),
('sys_voice_039', 'Rudolph', 'Rudolph', 'Rudolph', '红鼻子驯鹿鲁道夫的声音', 'holiday', 'other', 'en-US', 1, 39, datetime('now'), datetime('now')),

-- 英文音色
('sys_voice_040', 'Arnold', 'Arnold', 'Arnold', '阿诺德风格的英文男声', 'english', 'male', 'en-US', 1, 40, datetime('now'), datetime('now')),
('sys_voice_041', 'Charming_Santa', 'Charming Santa', 'Charming Santa', '魅力圣诞老人英文声音', 'english', 'male', 'en-US', 1, 41, datetime('now'), datetime('now')),
('sys_voice_042', 'Charming_Lady', 'Charming Lady', 'Charming Lady', '魅力女士英文声音', 'english', 'female', 'en-US', 1, 42, datetime('now'), datetime('now')),
('sys_voice_043', 'Sweet_Girl', 'Sweet Girl', 'Sweet Girl', '甜美女孩英文声音', 'english', 'female', 'en-US', 1, 43, datetime('now'), datetime('now')),
('sys_voice_044', 'Cute_Elf', 'Cute Elf', 'Cute Elf', '可爱精灵英文声音', 'english', 'other', 'en-US', 1, 44, datetime('now'), datetime('now')),
('sys_voice_045', 'Attractive_Girl', 'Attractive Girl', 'Attractive Girl', '迷人女孩英文声音', 'english', 'female', 'en-US', 1, 45, datetime('now'), datetime('now')),
('sys_voice_046', 'Serene_Woman', 'Serene Woman', 'Serene Woman', '宁静女性英文声音', 'english', 'female', 'en-US', 1, 46, datetime('now'), datetime('now'));

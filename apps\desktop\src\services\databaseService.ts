import { invoke } from '@tauri-apps/api/core';

/**
 * 数据库服务
 * 提供数据库相关的操作方法
 */
export class DatabaseService {
  /**
   * 强制运行数据库迁移
   * 用于修复数据库表缺失问题
   */
  static async forceRunMigrations(): Promise<string> {
    try {
      const result = await invoke<string>('force_run_database_migrations');
      console.log('数据库迁移结果:', result);
      return result;
    } catch (error) {
      console.error('强制运行数据库迁移失败:', error);
      throw error;
    }
  }

  /**
   * 检查数据库连接状态
   */
  static async checkConnection(): Promise<string> {
    try {
      const result = await invoke<string>('check_database_connection');
      return result;
    } catch (error) {
      console.error('检查数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 强制释放数据库连接
   * 紧急情况下使用
   */
  static async forceReleaseConnection(): Promise<string> {
    try {
      const result = await invoke<string>('force_release_database_connection');
      console.log('强制释放数据库连接结果:', result);
      return result;
    } catch (error) {
      console.error('强制释放数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 初始化数据库
   */
  static async initialize(): Promise<void> {
    try {
      await invoke('initialize_database');
      console.log('数据库初始化成功');
    } catch (error) {
      console.error('数据库初始化失败:', error);
      throw error;
    }
  }
}

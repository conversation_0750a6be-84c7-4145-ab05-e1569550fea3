import { invoke } from '@tauri-apps/api/core';
import {
  OutfitImageRecord,
  OutfitImageGenerationRequest,
  OutfitImageGenerationResponse,
  OutfitImageStats,
  ModelDashboardStats,
  OutfitImageRecordsResponse
} from '../types/outfitImage';

/**
 * 穿搭图片服务
 * 遵循 Tauri 开发规范的服务层设计原则
 */
export class OutfitImageService {
  /**
   * 获取模特个人看板统计信息
   */
  static async getModelDashboardStats(modelId: string): Promise<ModelDashboardStats> {
    try {
      const stats = await invoke<ModelDashboardStats>('get_model_dashboard_stats', {
        modelId
      });
      
      return stats;
    } catch (error) {
      console.error('❌ 获取模特个人看板统计信息失败:', error);
      throw new Error(`获取模特个人看板统计信息失败: ${error}`);
    }
  }

  /**
   * 获取模特的穿搭图片生成记录列表
   */
  static async getOutfitImageRecords(modelId: string): Promise<OutfitImageRecord[]> {
    try {
      console.log('📋 获取模特穿搭图片生成记录:', modelId);

      const records = await invoke<OutfitImageRecord[]>('get_outfit_image_records', {
        modelId
      });

      console.log('✅ 获取到穿搭图片记录:', records.length, '条');
      return records;
    } catch (error) {
      console.error('❌ 获取穿搭图片记录失败:', error);
      throw new Error(`获取穿搭图片记录失败: ${error}`);
    }
  }

  /**
   * 分页获取模特的穿搭图片生成记录列表
   */
  static async getOutfitImageRecordsPaginated(
    modelId: string,
    page: number = 1,
    pageSize: number = 20
  ): Promise<OutfitImageRecordsResponse> {
    try {

      const response = await invoke<OutfitImageRecordsResponse>('get_outfit_image_records_paginated', {
        modelId,
        page,
        pageSize
      });

      console.log('✅ 获取到穿搭图片记录:', response.records.length, '条', `(总数: ${response.total_count})`);
      return response;
    } catch (error) {
      console.error('❌ 分页获取穿搭图片记录失败:', error);
      throw new Error(`分页获取穿搭图片记录失败: ${error}`);
    }
  }

  /**
   * 创建穿搭图片生成记录
   */
  static async createOutfitImageRecord(request: OutfitImageGenerationRequest): Promise<string> {
    try {
      console.log('🎨 创建穿搭图片生成记录:', request);
      
      const recordId = await invoke<string>('create_outfit_image_record', {
        request
      });
      
      console.log('✅ 穿搭图片生成记录创建成功:', recordId);
      return recordId;
    } catch (error) {
      console.error('❌ 创建穿搭图片生成记录失败:', error);
      throw new Error(`创建穿搭图片生成记录失败: ${error}`);
    }
  }

  /**
   * 删除穿搭图片生成记录
   */
  static async deleteOutfitImageRecord(recordId: string): Promise<void> {
    try {
      console.log('🗑️ 删除穿搭图片生成记录:', recordId);
      
      await invoke<void>('delete_outfit_image_record', {
        recordId
      });
      
      console.log('✅ 穿搭图片生成记录删除成功');
    } catch (error) {
      console.error('❌ 删除穿搭图片生成记录失败:', error);
      throw new Error(`删除穿搭图片生成记录失败: ${error}`);
    }
  }

  /**
   * 获取穿搭图片生成记录详情
   */
  static async getOutfitImageRecordDetail(recordId: string): Promise<OutfitImageRecord | null> {
    try {
      console.log('🔍 获取穿搭图片生成记录详情:', recordId);
      
      const record = await invoke<OutfitImageRecord | null>('get_outfit_image_record_detail', {
        recordId
      });
      
      console.log('✅ 穿搭图片生成记录详情获取成功');
      return record;
    } catch (error) {
      console.error('❌ 获取穿搭图片生成记录详情失败:', error);
      throw new Error(`获取穿搭图片生成记录详情失败: ${error}`);
    }
  }

  /**
   * 创建穿搭图片生成任务（异步模式）
   */
  static async createOutfitImageTask(request: OutfitImageGenerationRequest): Promise<string> {
    try {
      console.log('🎨 创建穿搭图片生成任务:', request);

      // 只创建任务记录，不执行生成
      const recordId = await invoke<string>('create_outfit_image_record', {
        request
      });

      console.log('✅ 穿搭图片任务创建完成:', recordId);
      return recordId;
    } catch (error) {
      console.error('❌ 穿搭图片任务创建失败:', error);
      throw new Error(`穿搭图片任务创建失败: ${error}`);
    }
  }

  /**
   * 执行穿搭图片生成任务（后台异步执行）
   */
  static async executeOutfitImageTask(recordId: string): Promise<void> {
    try {
      console.log('🚀 开始执行穿搭图片生成任务:', recordId);

      // 异步执行生成任务，不等待结果
      invoke('execute_outfit_image_task', { recordId }).catch(error => {
        console.error('❌ 穿搭图片生成任务执行失败:', error);
      });

      console.log('✅ 穿搭图片生成任务已提交到后台');
    } catch (error) {
      console.error('❌ 穿搭图片任务执行失败:', error);
      throw new Error(`穿搭图片任务执行失败: ${error}`);
    }
  }

  /**
   * 生成穿搭图片（兼容旧接口，同步模式）
   */
  static async generateOutfitImages(request: OutfitImageGenerationRequest): Promise<OutfitImageGenerationResponse> {
    try {
      console.log('🎨 开始生成穿搭图片:', request);

      // 调用后端API执行实际的AI生成逻辑
      const response = await invoke<OutfitImageGenerationResponse>('execute_outfit_image_generation', {
        request
      });

      console.log('✅ 穿搭图片生成完成:', response);
      return response;
    } catch (error) {
      console.error('❌ 穿搭图片生成失败:', error);
      throw new Error(`穿搭图片生成失败: ${error}`);
    }
  }

  /**
   * 批量删除穿搭图片生成记录
   */
  static async batchDeleteOutfitImageRecords(recordIds: string[]): Promise<void> {
    try {
      console.log('🗑️ 批量删除穿搭图片生成记录:', recordIds.length, '条');
      
      const deletePromises = recordIds.map(recordId => 
        this.deleteOutfitImageRecord(recordId)
      );
      
      await Promise.all(deletePromises);
      
      console.log('✅ 批量删除穿搭图片生成记录成功');
    } catch (error) {
      console.error('❌ 批量删除穿搭图片生成记录失败:', error);
      throw new Error(`批量删除穿搭图片生成记录失败: ${error}`);
    }
  }

  /**
   * 获取模特的穿搭图片统计信息（从看板统计中提取）
   */
  static async getOutfitImageStats(modelId: string): Promise<OutfitImageStats> {
    try {
      const dashboardStats = await this.getModelDashboardStats(modelId);
      return dashboardStats.outfit_stats;
    } catch (error) {
      console.error('❌ 获取穿搭图片统计信息失败:', error);
      throw new Error(`获取穿搭图片统计信息失败: ${error}`);
    }
  }

  /**
   * 检查模特是否有穿搭图片记录
   */
  static async hasOutfitImageRecords(modelId: string): Promise<boolean> {
    try {
      const records = await this.getOutfitImageRecords(modelId);
      return records.length > 0;
    } catch (error) {
      console.error('❌ 检查穿搭图片记录失败:', error);
      return false;
    }
  }

  /**
   * 获取模特最近的穿搭图片生成记录
   */
  static async getRecentOutfitImageRecords(modelId: string, limit: number = 5): Promise<OutfitImageRecord[]> {
    try {
      const records = await this.getOutfitImageRecords(modelId);
      return records.slice(0, limit);
    } catch (error) {
      console.error('❌ 获取最近穿搭图片记录失败:', error);
      throw new Error(`获取最近穿搭图片记录失败: ${error}`);
    }
  }

  /**
   * 重试失败的穿搭图片生成任务
   */
  static async retryOutfitImageGeneration(recordId: string): Promise<void> {
    try {
      console.log('🔄 重试穿搭图片生成任务:', recordId);

      // 调用后端重试接口
      await invoke<void>('retry_outfit_image_generation', {
        recordId
      });

      console.log('✅ 穿搭图片生成任务重试成功');
    } catch (error) {
      console.error('❌ 重试穿搭图片生成任务失败:', error);
      throw new Error(`重试穿搭图片生成任务失败: ${error}`);
    }
  }

  /**
   * 批量创建穿搭图片生成任务（支持并发执行）
   */
  static async createBatchOutfitImageTasks(requests: OutfitImageGenerationRequest[]): Promise<string[]> {
    try {
      console.log('🎨 批量创建穿搭图片生成任务:', requests.length, '个');

      // 并发创建所有任务记录
      const createPromises = requests.map(request =>
        this.createOutfitImageTask(request)
      );

      const recordIds = await Promise.all(createPromises);

      console.log('✅ 批量穿搭图片任务创建完成:', recordIds);
      return recordIds;
    } catch (error) {
      console.error('❌ 批量穿搭图片任务创建失败:', error);
      throw new Error(`批量穿搭图片任务创建失败: ${error}`);
    }
  }

  /**
   * 批量执行穿搭图片生成任务（并发执行）
   */
  static async executeBatchOutfitImageTasks(recordIds: string[]): Promise<void> {
    try {
      console.log('🚀 批量执行穿搭图片生成任务:', recordIds.length, '个');

      // 并发执行所有任务，不等待结果
      const executePromises = recordIds.map(recordId =>
        this.executeOutfitImageTask(recordId)
      );

      await Promise.all(executePromises);

      console.log('✅ 批量穿搭图片生成任务已提交到后台');
    } catch (error) {
      console.error('❌ 批量穿搭图片任务执行失败:', error);
      throw new Error(`批量穿搭图片任务执行失败: ${error}`);
    }
  }

  /**
   * 一键批量生成穿搭图片（创建+执行）
   */
  static async batchGenerateOutfitImages(requests: OutfitImageGenerationRequest[]): Promise<string[]> {
    try {
      console.log('🎨 一键批量生成穿搭图片:', requests.length, '个任务');

      // 1. 批量创建任务记录
      const recordIds = await this.createBatchOutfitImageTasks(requests);

      // 2. 批量执行任务（并发）
      await this.executeBatchOutfitImageTasks(recordIds);

      console.log('✅ 批量穿搭图片生成完成，任务ID:', recordIds);
      return recordIds;
    } catch (error) {
      console.error('❌ 批量穿搭图片生成失败:', error);
      throw new Error(`批量穿搭图片生成失败: ${error}`);
    }
  }
}

/**
 * 声音克隆与TTS相关的类型定义
 * 基于海螺API接口规范
 */

// ============= 基础类型 =============

/**
 * 音频文件上传请求
 */
export interface AudioUploadRequest {
  /** 音频文件路径 */
  audio_file_path: string;
  /** 意图，默认voice_clone */
  purpose?: string;
}

/**
 * 音频文件上传响应
 */
export interface AudioUploadResponse {
  /** 上传状态 */
  status: boolean;
  /** 响应消息 */
  msg: string;
  /** 文件ID、URL或其他数据 */
  data?: string | number;
}

/**
 * 声音克隆请求
 */
export interface VoiceCloneRequest {
  /** 复刻的文本 */
  text: string;
  /** 支持的模型，默认speech-02-hd */
  model?: 'speech-02-hd' | 'speech-02-turbo' | 'speech-01-hd' | 'speech-01-turbo';
  /** 是否开启降噪，默认true */
  need_noise_reduction?: boolean;
  /** 音色克隆voice_id */
  voice_id?: string;
  /** 音色voice_id前缀，默认BoWong- */
  prefix?: string;
  /** 参考音频文件路径 */
  audio_file_path?: string;
}

/**
 * 声音克隆响应
 */
export interface VoiceCloneResponse {
  /** 克隆状态 */
  status: boolean;
  /** 响应消息 */
  msg: string;
  /** 音频URL */
  data?: string;
  /** 额外信息 */
  extra?: {
    /** 文件ID */
    file_id?: number;
    /** 生成的音色ID */
    voice_id?: string;
  };
}

/**
 * 音色信息
 */
export interface VoiceInfo {
  /** 音色ID */
  voice_id: string;
  /** 音色名称 */
  voice_name?: string;
  /** 音色描述 */
  description?: string[];
  /** 创建时间 */
  created_time?: string;
}

/**
 * 获取音色列表响应
 */
export interface GetVoicesResponse {
  /** 请求状态 */
  status: boolean;
  /** 响应消息 */
  msg: string;
  /** 音色列表 */
  data?: VoiceInfo[];
}

/**
 * 语音生成请求
 */
export interface SpeechGenerationRequest {
  /** TTS文本内容 */
  text: string;
  /** Voice ID */
  voice_id: string;
  /** 语速 [0.5, 2]，默认1.0 */
  speed?: number;
  /** 音量 (0,10]，默认1.0 */
  vol?: number;
  /** 情感 */
  emotion?: 'happy' | 'sad' | 'angry' | 'fearful' | 'disgusted' | 'surprised' | 'calm';
}

/**
 * 语音生成响应
 */
export interface SpeechGenerationResponse {
  /** 生成状态 */
  status: boolean;
  /** 响应消息 */
  msg: string;
  /** 音频URL */
  data?: string;
}

// ============= 前端状态管理类型 =============

/**
 * 音频上传状态
 */
export enum AudioUploadStatus {
  IDLE = 'idle',
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  ERROR = 'error'
}

/**
 * 声音克隆状态
 */
export enum VoiceCloneStatus {
  IDLE = 'idle',
  PROCESSING = 'processing',
  SUCCESS = 'success',
  ERROR = 'error'
}

/**
 * 语音生成状态
 */
export enum SpeechGenerationStatus {
  IDLE = 'idle',
  GENERATING = 'generating',
  SUCCESS = 'success',
  ERROR = 'error'
}

/**
 * 音频文件信息
 */
export interface AudioFileInfo {
  /** 文件对象 */
  file: File;
  /** 文件名 */
  name: string;
  /** 文件大小（字节） */
  size: number;
  /** 文件类型 */
  type: string;
  /** 文件时长（秒） */
  duration?: number;
  /** 预览URL */
  preview_url?: string;
}

/**
 * 音频上传状态信息
 */
export interface AudioUploadState {
  /** 上传状态 */
  status: AudioUploadStatus;
  /** 上传进度 (0-100) */
  progress: number;
  /** 错误信息 */
  error?: string;
  /** 上传结果 */
  result?: AudioUploadResponse;
}

/**
 * 声音克隆状态信息
 */
export interface VoiceCloneState {
  /** 克隆状态 */
  status: VoiceCloneStatus;
  /** 进度信息 */
  progress?: string;
  /** 错误信息 */
  error?: string;
  /** 克隆结果 */
  result?: VoiceCloneResponse;
}

/**
 * 语音生成状态信息
 */
export interface SpeechGenerationState {
  /** 生成状态 */
  status: SpeechGenerationStatus;
  /** 进度信息 */
  progress?: string;
  /** 错误信息 */
  error?: string;
  /** 生成结果 */
  result?: SpeechGenerationResponse;
}

/**
 * 音频播放器状态
 */
export interface AudioPlayerState {
  /** 是否正在播放 */
  isPlaying: boolean;
  /** 当前播放时间（秒） */
  currentTime: number;
  /** 总时长（秒） */
  duration: number;
  /** 音量 (0-1) */
  volume: number;
  /** 是否静音 */
  isMuted: boolean;
  /** 播放速度 */
  playbackRate: number;
}

// ============= 组件Props类型 =============

/**
 * 音频上传组件Props
 */
export interface AudioUploadProps {
  /** 上传状态 */
  uploadState: AudioUploadState;
  /** 文件选择回调 */
  onFileSelect: (file: File) => void;
  /** 上传回调 */
  onUpload: (file: File) => void;
  /** 清除回调 */
  onClear: () => void;
  /** 接受的文件类型 */
  accept?: string;
  /** 最大文件大小（字节） */
  maxSize?: number;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 音色管理组件Props
 */
export interface VoiceManagementProps {
  /** 音色列表 */
  voices: VoiceInfo[];
  /** 选中的音色ID */
  selectedVoiceId?: string;
  /** 音色选择回调 */
  onVoiceSelect: (voiceId: string) => void;
  /** 音色删除回调 */
  onVoiceDelete: (voiceId: string) => void;
  /** 刷新音色列表回调 */
  onRefresh: () => void;
  /** 是否正在加载 */
  loading?: boolean;
}

/**
 * 语音合成控制面板Props
 */
export interface SpeechControlProps {
  /** 生成请求参数 */
  request: SpeechGenerationRequest;
  /** 生成状态 */
  generationState: SpeechGenerationState;
  /** 参数变更回调 */
  onRequestChange: (request: SpeechGenerationRequest) => void;
  /** 生成回调 */
  onGenerate: () => void;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * 音频播放器组件Props
 */
export interface AudioPlayerProps {
  /** 音频URL */
  audioUrl?: string;
  /** 播放器状态 */
  playerState: AudioPlayerState;
  /** 播放状态变更回调 */
  onPlayStateChange: (isPlaying: boolean) => void;
  /** 时间变更回调 */
  onTimeChange: (currentTime: number) => void;
  /** 音量变更回调 */
  onVolumeChange: (volume: number) => void;
  /** 下载回调 */
  onDownload?: () => void;
  /** 是否显示下载按钮 */
  showDownload?: boolean;
}

// ============= 数据库记录类型 =============

/**
 * 语音生成记录状态
 */
export enum SpeechGenerationRecordStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

/**
 * 语音生成记录
 */
export interface SpeechGenerationRecord {
  /** 记录ID */
  id: string;
  /** 生成的文本内容 */
  text: string;
  /** 使用的音色ID */
  voice_id: string;
  /** 音色名称 */
  voice_name?: string;
  /** 语速 */
  speed: number;
  /** 音量 */
  volume: number;
  /** 情感 */
  emotion?: string;
  /** 生成的音频URL */
  audio_url?: string;
  /** 本地文件路径 */
  local_file_path?: string;
  /** 记录状态 */
  status: SpeechGenerationRecordStatus;
  /** 错误信息 */
  error_message?: string;
  /** 创建时间 */
  created_at: string;
  /** 开始时间 */
  started_at?: string;
  /** 完成时间 */
  completed_at?: string;
  /** 生成耗时（毫秒） */
  duration_ms?: number;
}

/**
 * 创建语音生成记录请求
 */
export interface CreateSpeechGenerationRecordRequest {
  /** 生成的文本内容 */
  text: string;
  /** 使用的音色ID */
  voice_id: string;
  /** 音色名称 */
  voice_name?: string;
  /** 语速 */
  speed: number;
  /** 音量 */
  volume: number;
  /** 情感 */
  emotion?: string;
  /** 生成的音频URL */
  audio_url?: string;
  /** 本地文件路径 */
  local_file_path?: string;
}

// ============= 工具配置类型 =============

/**
 * 声音克隆工具配置
 */
export interface VoiceCloneToolConfig {
  /** 支持的音频格式 */
  supportedFormats: string[];
  /** 最大文件大小（字节） */
  maxFileSize: number;
  /** 默认模型 */
  defaultModel: string;
  /** 默认语音参数 */
  defaultSpeechParams: {
    speed: number;
    volume: number;
    emotion: string;
  };
  /** API配置 */
  apiConfig: {
    baseUrl: string;
    bearerToken: string;
    timeout: number;
  };
}

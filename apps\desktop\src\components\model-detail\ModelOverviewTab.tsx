import React from 'react';
import { UserIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { Model } from '../../types/model';
import { ModelDashboardStats } from '../../types/outfitImage';
import { VideoGenerationTask } from '../../types/videoGeneration';
import { OutfitImageRecord } from '../../types/outfitImage';
import { ModelDashboardStatsComponent } from '../ModelDashboardStats';
import { TabId } from './ModelDetailTabs';

interface ModelOverviewTabProps {
  model: Model;
  dashboardStats: ModelDashboardStats | null;
  statsLoading: boolean;
  videoTasks: VideoGenerationTask[];
  outfitRecords: OutfitImageRecord[];
  onTabChange: (tabId: TabId) => void;
}

/**
 * 模特详情概览Tab组件
 * 包含基本信息、统计看板和快速操作区域
 */
export const ModelOverviewTab: React.FC<ModelOverviewTabProps> = ({
  model,
  dashboardStats,
  statsLoading,
  videoTasks,
  outfitRecords,
  onTabChange
}) => {
  const getGenderText = (gender: string) => {
    switch (gender) {
      case 'Male': return '男';
      case 'Female': return '女';
      default: return '其他';
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      {/* 基本信息和统计 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 模特基本信息 */}
        <div className="bg-gradient-to-br from-white to-primary-50/30 rounded-2xl shadow-sm border border-gray-200/50 p-6 hover:shadow-md transition-all duration-300 hover:-translate-y-1 relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-primary-100/50 to-primary-200/50 rounded-full -translate-y-8 translate-x-8 opacity-50"></div>

          <div className="relative z-10">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center text-white mr-3">
                <UserIcon className="w-5 h-5" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">基本信息</h3>
            </div>

            <div className="space-y-3">
              <div className="flex items-center justify-between py-2">
                <span className="text-sm text-gray-600">性别</span>
                <span className="text-sm font-medium text-gray-900">{getGenderText(model.gender)}</span>
              </div>
              {model.age && (
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-gray-600">年龄</span>
                  <span className="text-sm font-medium text-gray-900">{model.age}岁</span>
                </div>
              )}
              {model.height && (
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-gray-600">身高</span>
                  <span className="text-sm font-medium text-gray-900">{model.height}cm</span>
                </div>
              )}
              {model.weight && (
                <div className="flex items-center justify-between py-2">
                  <span className="text-sm text-gray-600">体重</span>
                  <span className="text-sm font-medium text-gray-900">{model.weight}kg</span>
                </div>
              )}
            </div>

            {model.description && (
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <h4 className="text-sm font-medium text-gray-700 mb-1">个人描述</h4>
                <p className="text-sm text-gray-600 leading-relaxed">{model.description}</p>
              </div>
            )}

            {model.tags.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">标签</h4>
                <div className="flex flex-wrap gap-2">
                  {model.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-primary-100 text-primary-800 text-xs rounded-full font-medium"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 个人看板统计信息 */}
        {dashboardStats && (
          <ModelDashboardStatsComponent
            stats={dashboardStats}
            loading={statsLoading}
          />
        )}
      </div>

      {/* 快速操作区域 */}
      <div className="bg-gradient-to-br from-white to-gray-50/30 rounded-2xl shadow-sm border border-gray-200/50 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <span>⚡</span>
          快速操作
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button
            onClick={() => onTabChange('photos')}
            className="flex items-center gap-3 p-4 bg-white rounded-xl border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-all duration-200 group"
          >
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
              <span className="text-lg">📸</span>
            </div>
            <div className="text-left">
              <div className="font-medium text-gray-900">管理照片</div>
              <div className="text-sm text-gray-500">{model.photos.length} 张照片</div>
            </div>
          </button>

          <button
            onClick={() => onTabChange('videos')}
            className="flex items-center gap-3 p-4 bg-white rounded-xl border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-all duration-200 group"
          >
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center group-hover:bg-red-200 transition-colors">
              <span className="text-lg">🎬</span>
            </div>
            <div className="text-left">
              <div className="font-medium text-gray-900">生成视频</div>
              <div className="text-sm text-gray-500">{videoTasks.length} 个任务</div>
            </div>
          </button>

          <button
            onClick={() => onTabChange('outfits')}
            className="flex items-center gap-3 p-4 bg-white rounded-xl border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-all duration-200 group"
          >
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center group-hover:bg-purple-200 transition-colors">
              <span className="text-lg">👗</span>
            </div>
            <div className="text-left">
              <div className="font-medium text-gray-900">生成穿搭</div>
              <div className="text-sm text-gray-500">{outfitRecords.length} 个记录</div>
            </div>
          </button>

          <button
            onClick={() => onTabChange('dynamics')}
            className="flex items-center gap-3 p-4 bg-white rounded-xl border border-gray-200 hover:border-primary-300 hover:bg-primary-50 transition-all duration-200 group"
          >
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
              <SparklesIcon className="w-5 h-5 text-green-600" />
            </div>
            <div className="text-left">
              <div className="font-medium text-gray-900">模特动态</div>
              <div className="text-sm text-gray-500">查看AI生成动态</div>
            </div>
          </button>
        </div>
      </div>
    </div>
  );
};

-- 创建穿搭图片生成记录表
CREATE TABLE IF NOT EXISTS outfit_image_records (
    id TEXT PRIMARY KEY,
    model_id TEXT NOT NULL,
    model_image_id TEXT NOT NULL, -- 使用的模特形象图片ID
    generation_prompt TEXT, -- 生成提示词
    status TEXT NOT NULL DEFAULT 'pending', -- pending, processing, completed, failed
    progress REAL NOT NULL DEFAULT 0.0,
    result_urls TEXT NOT NULL DEFAULT '[]', -- JSON数组，存储生成的穿搭图片URLs
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    started_at DATETIME,
    completed_at DATETIME,
    duration_ms INTEGER,
    FOREIGN KEY (model_id) REFERENCES models (id) ON DELETE CASCADE,
    FOREIGN KEY (model_image_id) REFERENCES model_photos (id) ON DELETE CASCADE
);

-- 创建商品图片表
CREATE TABLE IF NOT EXISTS product_images (
    id TEXT PRIMARY KEY,
    outfit_record_id TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_size INTEGER NOT NULL,
    upload_url TEXT, -- 上传到云端的URL
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (outfit_record_id) REFERENCES outfit_image_records (id) ON DELETE CASCADE
);

-- 创建穿搭图片表（存储生成的结果图片详细信息）
CREATE TABLE IF NOT EXISTS outfit_images (
    id TEXT PRIMARY KEY,
    outfit_record_id TEXT NOT NULL,
    image_url TEXT NOT NULL,
    local_path TEXT, -- 本地缓存路径
    image_index INTEGER NOT NULL, -- 在生成结果中的索引
    description TEXT,
    tags TEXT, -- JSON数组，存储标签
    is_favorite BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (outfit_record_id) REFERENCES outfit_image_records (id) ON DELETE CASCADE
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_outfit_image_records_model_id ON outfit_image_records (model_id);
CREATE INDEX IF NOT EXISTS idx_outfit_image_records_status ON outfit_image_records (status);
CREATE INDEX IF NOT EXISTS idx_outfit_image_records_created_at ON outfit_image_records (created_at DESC);

CREATE INDEX IF NOT EXISTS idx_product_images_outfit_record_id ON product_images (outfit_record_id);
CREATE INDEX IF NOT EXISTS idx_product_images_created_at ON product_images (created_at DESC);

CREATE INDEX IF NOT EXISTS idx_outfit_images_outfit_record_id ON outfit_images (outfit_record_id);
CREATE INDEX IF NOT EXISTS idx_outfit_images_is_favorite ON outfit_images (is_favorite);
CREATE INDEX IF NOT EXISTS idx_outfit_images_created_at ON outfit_images (created_at DESC);

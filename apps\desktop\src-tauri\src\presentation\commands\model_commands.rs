use tauri::{command, State};
use crate::app_state::AppState;
use crate::business::services::model_service::{ModelService, ModelStatistics};
use crate::data::models::model::{
    Model, ModelPhoto, CreateModelRequest, UpdateModelRequest, 
    ModelQueryParams, PhotoType, ModelStatus
};

/// 创建模特命令
/// 遵循 Tauri 开发规范的命令设计模式
#[command]
pub async fn create_model(
    state: State<'_, AppState>,
    request: CreateModelRequest,
) -> Result<Model, String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::create_model(repository, request)
        .map_err(|e| e.to_string())
}

/// 获取模特详情命令
#[command]
pub async fn get_model_by_id(
    state: State<'_, AppState>,
    id: String,
) -> Result<Option<Model>, String> {

    let repository_guard = state.get_model_repository()
        .map_err(|e| {
            println!("获取模特仓库失败: {}", e);
            format!("获取模特仓库失败: {}", e)
        })?;

    let repository = repository_guard.as_ref()
        .ok_or_else(|| {
            println!("模特仓库未初始化");
            "模特仓库未初始化".to_string()
        })?;

    let result = ModelService::get_model_by_id(repository, &id)
        .map_err(|e| {
            println!("ModelService::get_model_by_id 失败: {}", e);
            e.to_string()
        });

    result
}

/// 获取所有模特命令
#[command]
pub async fn get_all_models(
    state: State<'_, AppState>,
) -> Result<Vec<Model>, String> {
    println!("开始获取所有模特...");

    let repository_guard = state.get_model_repository()
        .map_err(|e| {
            println!("获取模特仓库失败: {}", e);
            format!("获取模特仓库失败: {}", e)
        })?;

    let repository = repository_guard.as_ref()
        .ok_or_else(|| {
            println!("模特仓库未初始化");
            "模特仓库未初始化".to_string()
        })?;

    let result = ModelService::get_all_models(repository)
        .map_err(|e| {
            println!("ModelService::get_all_models 失败: {}", e);
            e.to_string()
        });

    println!("get_all_models 结果: {:?}", result.is_ok());
    result
}

/// 搜索模特命令
#[command]
pub async fn search_models(
    state: State<'_, AppState>,
    params: ModelQueryParams,
) -> Result<Vec<Model>, String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::search_models(repository, params)
        .map_err(|e| e.to_string())
}

/// 更新模特命令
#[command]
pub async fn update_model(
    state: State<'_, AppState>,
    id: String,
    request: UpdateModelRequest,
) -> Result<Model, String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::update_model(repository, &id, request)
        .map_err(|e| e.to_string())
}

/// 删除模特命令
#[command]
pub async fn delete_model(
    state: State<'_, AppState>,
    id: String,
) -> Result<(), String> {
    println!("开始删除模特，ID: {}", id);

    let repository_guard = state.get_model_repository()
        .map_err(|e| {
            println!("获取模特仓库失败: {}", e);
            format!("获取模特仓库失败: {}", e)
        })?;

    let repository = repository_guard.as_ref()
        .ok_or_else(|| {
            println!("模特仓库未初始化");
            "模特仓库未初始化".to_string()
        })?;

    println!("调用 ModelService::delete_model");
    let result = ModelService::delete_model(repository, &id)
        .map_err(|e| {
            println!("删除模特失败: {}", e);
            e.to_string()
        });

    if result.is_ok() {
        println!("模特删除成功");
    }

    result
}

/// 永久删除模特命令
#[command]
pub async fn delete_model_permanently(
    state: State<'_, AppState>,
    id: String,
) -> Result<(), String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::delete_model_permanently(repository, &id)
        .map_err(|e| e.to_string())
}

/// 添加模特照片命令
#[command]
pub async fn add_model_photo(
    state: State<'_, AppState>,
    model_id: String,
    file_path: String,
    photo_type: PhotoType,
    description: Option<String>,
    tags: Option<Vec<String>>,
) -> Result<ModelPhoto, String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::add_model_photo(repository, &model_id, file_path, photo_type, description, tags)
        .map_err(|e| e.to_string())
}

/// 删除模特照片命令
#[command]
pub async fn delete_model_photo(
    state: State<'_, AppState>,
    model_id: String,
    photo_id: String,
) -> Result<(), String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::delete_model_photo(repository, &model_id, &photo_id)
        .map_err(|e| e.to_string())
}

/// 设置封面照片命令
#[command]
pub async fn set_cover_photo(
    state: State<'_, AppState>,
    model_id: String,
    photo_id: String,
) -> Result<(), String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::set_cover_photo(repository, &model_id, &photo_id)
        .map_err(|e| e.to_string())
}

/// 添加标签命令
#[command]
pub async fn add_model_tag(
    state: State<'_, AppState>,
    model_id: String,
    tag: String,
) -> Result<(), String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::add_tag(repository, &model_id, tag)
        .map_err(|e| e.to_string())
}

/// 移除标签命令
#[command]
pub async fn remove_model_tag(
    state: State<'_, AppState>,
    model_id: String,
    tag: String,
) -> Result<(), String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::remove_tag(repository, &model_id, &tag)
        .map_err(|e| e.to_string())
}

/// 更新模特状态命令
#[command]
pub async fn update_model_status(
    state: State<'_, AppState>,
    model_id: String,
    status: ModelStatus,
) -> Result<(), String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::update_model_status(repository, &model_id, status)
        .map_err(|e| e.to_string())
}

/// 设置模特评分命令
#[command]
pub async fn set_model_rating(
    state: State<'_, AppState>,
    model_id: String,
    rating: Option<f32>,
) -> Result<(), String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::set_model_rating(repository, &model_id, rating)
        .map_err(|e| e.to_string())
}

/// 设置头像命令
#[command]
pub async fn set_model_avatar(
    state: State<'_, AppState>,
    model_id: String,
    avatar_path: Option<String>,
) -> Result<(), String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::set_avatar(repository, &model_id, avatar_path)
        .map_err(|e| e.to_string())
}

/// 获取模特统计信息命令
#[command]
pub async fn get_model_statistics(
    state: State<'_, AppState>,
) -> Result<ModelStatistics, String> {
    let repository_guard = state.get_model_repository()
        .map_err(|e| format!("获取模特仓库失败: {}", e))?;
    
    let repository = repository_guard.as_ref()
        .ok_or("模特仓库未初始化")?;

    ModelService::get_model_statistics(repository)
        .map_err(|e| e.to_string())
}

/// 选择照片文件命令
#[command]
pub async fn select_photo_files() -> Result<Vec<String>, String> {
    // TODO: 实现文件选择功能
    // 暂时返回空列表，等待 Tauri 文件对话框插件配置完成
    Ok(Vec::new())
}

/// 选择单个照片文件命令
#[command]
pub async fn select_photo_file() -> Result<Option<String>, String> {
    // TODO: 实现文件选择功能
    // 暂时返回 None，等待 Tauri 文件对话框插件配置完成
    Ok(None)
}
